#pragma once

#include <string>
#include <string_view>
#include <memory>
#include <unordered_map>
#include <vector>
#include <optional>
#include <variant>
#include <mutex>
#include <chrono>
#include <thread>
#include <yaml-cpp/yaml.h>

#include "exceptions.h"
#include "load/loader_strategies.h" // For LoadingStrategy and related types

namespace omop::common {

// Forward declarations
class ConfigurationEncryptor;

/**
 * @brief Represents a single transformation rule in the ETL pipeline
 *
 * This class encapsulates the mapping rules from source columns to target columns,
 * including the transformation type and any additional parameters needed for the
 * transformation process.
 */
class TransformationRule {
public:
    /**
     * @brief Enumeration of supported transformation types
     */
    enum class Type {
        Direct,              ///< Direct column mapping without transformation
        DateTransform,       ///< Date format transformation
        VocabularyMapping,   ///< Map using vocabulary lookup
        DateCalculation,     ///< Calculate date based on multiple fields
        NumericTransform,    ///< Numeric value transformation
        StringConcatenation, ///< Concatenate multiple string fields
        Conditional,         ///< Conditional transformation based on rules
        Custom              ///< Custom transformation logic
    };

    /**
     * @brief Default constructor
     */
    TransformationRule() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing transformation configuration
     */
    explicit TransformationRule(const YAML::Node& node);

    /**
     * @brief Get source column name
     * @return std::string_view Source column name
     */
    [[nodiscard]] std::string_view source_column() const noexcept { return source_column_; }

    /**
     * @brief Get source columns for multi-column transformations
     * @return const std::vector<std::string>& Vector of source column names
     */
    [[nodiscard]] const std::vector<std::string>& source_columns() const noexcept {
        return source_columns_;
    }

    /**
     * @brief Get target column name
     * @return std::string_view Target column name
     */
    [[nodiscard]] std::string_view target_column() const noexcept { return target_column_; }

    /**
     * @brief Get transformation type
     * @return Type The transformation type
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get transformation parameters
     * @return const YAML::Node& Additional parameters for the transformation
     */
    [[nodiscard]] const YAML::Node& parameters() const noexcept { return parameters_; }

    /**
     * @brief Check if this is a multi-column transformation
     * @return bool True if multiple source columns are involved
     */
    [[nodiscard]] bool is_multi_column() const noexcept { return !source_columns_.empty(); }

private:
    std::string source_column_;              ///< Single source column name
    std::vector<std::string> source_columns_; ///< Multiple source columns
    std::string target_column_;              ///< Target column name
    Type type_{Type::Direct};                ///< Transformation type
    YAML::Node parameters_;                  ///< Additional parameters
};

/**
 * @brief Configuration for a single table mapping
 *
 * This class represents the complete mapping configuration for transforming
 * data from a source table to an OMOP CDM target table.
 */
class TableMapping {
public:
    /**
     * @brief Default constructor
     */
    TableMapping() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing table mapping configuration
     */
    explicit TableMapping(const YAML::Node& node);

    /**
     * @brief Get source table name
     * @return std::string_view Source table name
     */
    [[nodiscard]] std::string_view source_table() const noexcept { return source_table_; }

    /**
     * @brief Get target table name
     * @return std::string_view Target table name
     */
    [[nodiscard]] std::string_view target_table() const noexcept { return target_table_; }

    /**
     * @brief Get transformation rules
     * @return const std::vector<TransformationRule>& Vector of transformation rules
     */
    [[nodiscard]] const std::vector<TransformationRule>& transformations() const noexcept {
        return transformations_;
    }

    /**
     * @brief Get pre-processing SQL query
     * @return std::optional<std::string> Optional pre-processing SQL
     */
    [[nodiscard]] const std::optional<std::string>& pre_process_sql() const noexcept {
        return pre_process_sql_;
    }

    /**
     * @brief Get post-processing SQL query
     * @return std::optional<std::string> Optional post-processing SQL
     */
    [[nodiscard]] const std::optional<std::string>& post_process_sql() const noexcept {
        return post_process_sql_;
    }

    /**
     * @brief Get filter conditions
     * @return const YAML::Node& Filter conditions as YAML node
     */
    [[nodiscard]] const YAML::Node& filters() const noexcept { return filters_; }

    /**
     * @brief Get validation rules
     * @return const YAML::Node& Validation rules as YAML node
     */
    [[nodiscard]] const YAML::Node& validations() const noexcept { return validations_; }

private:
    std::string source_table_;                    ///< Source table name
    std::string target_table_;                    ///< Target table name
    std::vector<TransformationRule> transformations_; ///< Transformation rules
    std::optional<std::string> pre_process_sql_;  ///< Pre-processing SQL
    std::optional<std::string> post_process_sql_; ///< Post-processing SQL
    YAML::Node filters_;                          ///< Filter conditions
    YAML::Node validations_;                      ///< Validation rules
};

/**
 * @brief Database connection configuration
 */
class DatabaseConfig {
public:
    /**
     * @brief Database types supported
     */
    enum class Type {
        PostgreSQL,
        MySQL,
        MSSQL,
        Oracle
    };

    /**
     * @brief Default constructor
     */
    DatabaseConfig() = default;

    /**
     * @brief Construct from YAML node
     * @param node YAML node containing database configuration
     */
    explicit DatabaseConfig(const YAML::Node& node);

    /**
     * @brief Get database type
     * @return Type Database type
     */
    [[nodiscard]] Type type() const noexcept { return type_; }

    /**
     * @brief Get connection string
     * @return std::string_view Connection string
     */
    [[nodiscard]] std::string_view connection_string() const noexcept {
        return connection_string_;
    }

    /**
     * @brief Get host name
     * @return std::string_view Host name
     */
    [[nodiscard]] std::string_view host() const noexcept { return host_; }

    /**
     * @brief Get port number
     * @return int Port number
     */
    [[nodiscard]] int port() const noexcept { return port_; }

    /**
     * @brief Get database name
     * @return std::string_view Database name
     */
    [[nodiscard]] std::string_view database() const noexcept { return database_; }

    /**
     * @brief Get username
     * @return std::string_view Username
     */
    [[nodiscard]] std::string_view username() const noexcept { return username_; }

    /**
     * @brief Get password
     * @return std::string_view Password
     */
    [[nodiscard]] std::string_view password() const noexcept { return password_; }

    /**
     * @brief Get additional connection parameters
     * @return const std::unordered_map<std::string, std::string>& Additional parameters
     */
    [[nodiscard]] const std::unordered_map<std::string, std::string>& parameters() const noexcept {
        return parameters_;
    }

private:
    Type type_{Type::PostgreSQL};
    std::string connection_string_;
    std::string host_;
    int port_{5432};
    std::string database_;
    std::string username_;
    std::string password_;
    std::unordered_map<std::string, std::string> parameters_;
};

/**
 * @brief Loading configuration for ETL pipeline
 * 
 * Contains configuration for loading strategies including incremental
 * processing options like CDC and watermarking.
 */
class LoadingConfiguration {
public:
    /**
     * @brief Default constructor
     */
    LoadingConfiguration() = default;
    
    /**
     * @brief Construct from YAML node
     * @param node YAML node containing loading configuration
     */
    explicit LoadingConfiguration(const YAML::Node& node);
    
    /**
     * @brief Get loading strategy type
     * @return omop::load::LoadingStrategy Strategy type
     */
    [[nodiscard]] omop::load::LoadingStrategy get_strategy() const noexcept {
        return strategy_;
    }
    
    /**
     * @brief Get loading mode
     * @return omop::load::LoadingMode Loading mode
     */
    [[nodiscard]] omop::load::LoadingMode get_mode() const noexcept {
        return mode_;
    }
    
    /**
     * @brief Get batch size
     * @return size_t Batch size
     */
    [[nodiscard]] size_t get_batch_size() const noexcept {
        return batch_size_;
    }
    
    /**
     * @brief Get parallel workers count
     * @return size_t Number of parallel workers
     */
    [[nodiscard]] size_t get_parallel_workers() const noexcept {
        return parallel_workers_;
    }
    
    /**
     * @brief Check if CDC is enabled
     * @return bool True if CDC enabled
     */
    [[nodiscard]] bool is_cdc_enabled() const noexcept {
        return enable_cdc_;
    }
    
    /**
     * @brief Get CDC configuration
     * @return const omop::load::CDCConfig& CDC configuration
     */
    [[nodiscard]] const omop::load::CDCConfig& get_cdc_config() const noexcept {
        return cdc_config_;
    }
    
    /**
     * @brief Get watermark information
     * @return const omop::load::WatermarkInfo& Watermark configuration
     */
    [[nodiscard]] const omop::load::WatermarkInfo& get_watermark_info() const noexcept {
        return watermark_info_;
    }
    
    /**
     * @brief Get watermark column name
     * @return std::string_view Watermark column
     */
    [[nodiscard]] std::string_view get_watermark_column() const noexcept {
        return watermark_column_;
    }
    
    /**
     * @brief Get watermark file path
     * @return std::string_view Watermark file path
     */
    [[nodiscard]] std::string_view get_watermark_file() const noexcept {
        return watermark_file_;
    }
    
    /**
     * @brief Check if hash comparison is enabled
     * @return bool True if hash comparison enabled
     */
    [[nodiscard]] bool is_hash_comparison_enabled() const noexcept {
        return enable_hash_comparison_;
    }
    
    /**
     * @brief Get tracking columns for change detection
     * @return const std::vector<std::string>& Tracking columns
     */
    [[nodiscard]] const std::vector<std::string>& get_tracking_columns() const noexcept {
        return tracking_columns_;
    }
    
    /**
     * @brief Get CDC table prefix
     * @return std::string_view CDC table prefix
     */
    [[nodiscard]] std::string_view get_cdc_table_prefix() const noexcept {
        return cdc_table_prefix_;
    }
    
    /**
     * @brief Convert to LoadingConfig struct
     * @return omop::load::LoadingConfig Loading configuration struct
     */
    [[nodiscard]] omop::load::LoadingConfig to_loading_config() const;
    
private:
    omop::load::LoadingStrategy strategy_{omop::load::LoadingStrategy::BatchInsert};
    omop::load::LoadingMode mode_{omop::load::LoadingMode::Insert};
    size_t batch_size_{1000};
    size_t parallel_workers_{1};
    
    // Incremental processing settings
    bool enable_cdc_{false};
    omop::load::CDCConfig cdc_config_;
    omop::load::WatermarkInfo watermark_info_;
    std::string watermark_column_;
    std::string watermark_file_;
    bool enable_hash_comparison_{true};
    std::vector<std::string> tracking_columns_;
    std::string cdc_table_prefix_{"cdc_"};
};

/**
 * @brief Main configuration manager for the ETL pipeline
 *
 * This class manages all configuration aspects of the OMOP ETL pipeline,
 * including loading configuration files, managing table mappings, and
 * providing access to various configuration parameters.
 */
class ConfigurationManager {
public:
    /**
     * @brief Construct a new ConfigurationManager
     */
    ConfigurationManager() = default;

    /**
     * @brief Load configuration from a YAML file
     * @param filepath Path to the YAML configuration file
     * @throws ConfigurationException if the file cannot be loaded or parsed
     */
    void load_config(const std::string& filepath);

    /**
     * @brief Load configuration from a YAML string
     * @param yaml_content YAML content as string
     * @throws ConfigurationException if the content cannot be parsed
     */
    void load_config_from_string(const std::string& yaml_content);

    /**
     * @brief Get table mapping by name
     * @param table_name Name of the table
     * @return std::optional<TableMapping> Table mapping if found
     */
    [[nodiscard]] std::optional<TableMapping> get_table_mapping(
        const std::string& table_name) const;

    /**
     * @brief Get all table mappings
     * @return const std::unordered_map<std::string, TableMapping>& All table mappings
     */
    [[nodiscard]] const std::unordered_map<std::string, TableMapping>&
        get_all_mappings() const noexcept {
        return table_mappings_;
    }

    /**
     * @brief Get source database configuration
     * @return const DatabaseConfig& Source database configuration
     */
    [[nodiscard]] const DatabaseConfig& get_source_db() const noexcept {
        return source_db_;
    }

    /**
     * @brief Get target database configuration
     * @return const DatabaseConfig& Target database configuration
     */
    [[nodiscard]] const DatabaseConfig& get_target_db() const noexcept {
        return target_db_;
    }

    /**
     * @brief Get configuration value by key
     * @param key Configuration key in dot notation (e.g., "etl.batch_size")
     * @return std::optional<YAML::Node> Configuration value if found
     */
    [[nodiscard]] std::optional<YAML::Node> get_value(const std::string& key) const;

    /**
     * @brief Get configuration value with default
     * @tparam T Type of the value
     * @param key Configuration key
     * @param default_value Default value if key not found
     * @return T Configuration value or default
     */
    template<typename T>
    [[nodiscard]] T get_value_or(const std::string& key, const T& default_value) const {
        auto value = get_value(key);
        if (value && value->IsDefined()) {
            try {
                return value->as<T>();
            } catch (const YAML::Exception&) {
                return default_value;
            }
        }
        return default_value;
    }

    /**
     * @brief Validate configuration
     * @throws ConfigurationException if configuration is invalid
     */
    void validate_config() const;

    /**
     * @brief Check if configuration is loaded
     * @return bool True if configuration is loaded
     */
    [[nodiscard]] bool is_loaded() const noexcept { return config_loaded_; }

    /**
     * @brief Get vocabulary mappings
     * @return const YAML::Node& Vocabulary mappings configuration
     */
    [[nodiscard]] const YAML::Node& get_vocabulary_mappings() const noexcept {
        return vocabulary_mappings_;
    }

    /**
     * @brief Get ETL settings
     * @return const YAML::Node& ETL settings configuration
     */
    [[nodiscard]] const YAML::Node& get_etl_settings() const noexcept {
        return etl_settings_;
    }
    
    /**
     * @brief Get loading configuration
     * @return const LoadingConfiguration& Loading configuration
     */
    [[nodiscard]] const LoadingConfiguration& get_loading_config() const noexcept {
        return loading_config_;
    }
    
    /**
     * @brief Get loading configuration for specific table
     * @param table_name Table name
     * @return std::optional<LoadingConfiguration> Table-specific loading configuration
     */
    [[nodiscard]] std::optional<LoadingConfiguration> get_table_loading_config(
        const std::string& table_name) const;
    
    /**
     * @brief Check if incremental processing is enabled globally
     * @return bool True if incremental processing enabled
     */
    [[nodiscard]] bool is_incremental_processing_enabled() const noexcept {
        return incremental_processing_enabled_;
    }
    
    /**
     * @brief Get default watermark directory
     * @return std::string_view Default watermark directory path
     */
    [[nodiscard]] std::string_view get_default_watermark_directory() const noexcept {
        return default_watermark_directory_;
    }
    
    /**
     * @brief Clear all configuration data
     */
    void clear();
    
    /**
     * @brief Get the path of the loaded configuration file
     * @return std::string Path to configuration file (empty if loaded from string)
     */
    [[nodiscard]] std::string get_config_file_path() const noexcept;
    
    /**
     * @brief Get the time when configuration was loaded
     * @return std::chrono::system_clock::time_point Load timestamp
     */
    [[nodiscard]] std::chrono::system_clock::time_point get_load_time() const noexcept;
    
    /**
     * @brief Reload configuration from file
     * @throws ConfigurationException if no file was previously loaded or reload fails
     */
    void reload();
    
    /**
     * @brief Set encryption/decryption handler
     * @param encryptor Pointer to configuration encryptor (nullptr to disable)
     */
    void set_encryptor(ConfigurationEncryptor* encryptor) noexcept;
    
    /**
     * @brief Enable compatibility mode for older API versions
     * @param version Version to provide compatibility for (e.g., "1.0")
     */
    void enable_compatibility_mode(const std::string& version);
    
    /**
     * @brief Get current configuration version
     * @return std::string Configuration version
     */
    [[nodiscard]] std::string get_version() const noexcept;

private:
    /**
     * @brief Parse table mappings from configuration
     * @param mappings_node YAML node containing table mappings
     */
    void parse_table_mappings(const YAML::Node& mappings_node);

    /**
     * @brief Parse database configuration
     * @param db_node YAML node containing database configuration
     * @return DatabaseConfig Parsed database configuration
     */
    DatabaseConfig parse_database_config(const YAML::Node& db_node);
    
    /**
     * @brief Parse loading configuration from YAML
     * @param loading_node YAML node containing loading configuration
     */
    void parse_loading_config(const YAML::Node& loading_node);
    
    /**
     * @brief Parse CDC configuration from YAML
     * @param cdc_node YAML node containing CDC configuration
     * @return omop::load::CDCConfig CDC configuration
     */
    omop::load::CDCConfig parse_cdc_config(const YAML::Node& cdc_node);
    
    /**
     * @brief Parse watermark configuration from YAML
     * @param watermark_node YAML node containing watermark configuration
     * @return omop::load::WatermarkInfo Watermark configuration
     */
    omop::load::WatermarkInfo parse_watermark_config(const YAML::Node& watermark_node);

    mutable std::mutex config_mutex_;
    bool config_loaded_{false};
    YAML::Node root_config_;
    std::unordered_map<std::string, TableMapping> table_mappings_;
    DatabaseConfig source_db_;
    DatabaseConfig target_db_;
    YAML::Node vocabulary_mappings_;
    YAML::Node etl_settings_;
    YAML::Node tables_node_;
    LoadingConfiguration loading_config_;
    std::unordered_map<std::string, LoadingConfiguration> table_loading_configs_;
    bool incremental_processing_enabled_{false};
    std::string default_watermark_directory_{"/tmp/omop-etl/watermarks"};
    std::string config_file_path_;
    std::chrono::system_clock::time_point load_time_;
    ConfigurationEncryptor* encryptor_{nullptr};
    std::string compatibility_version_;
    std::string config_version_{"2.0"};
};

/**
 * @brief Singleton accessor for global configuration
 *
 * Provides global access to the configuration manager instance.
 * This follows the singleton pattern to ensure consistent configuration
 * across the entire application.
 */
class Config {
public:
    /**
     * @brief Get the singleton instance
     * @return ConfigurationManager& Reference to the configuration manager
     */
    [[nodiscard]] static ConfigurationManager& instance() {
        std::call_once(init_flag_, []() {
            instance_.reset(new ConfigurationManager());
        });
        return *instance_;
    }

    Config() = delete;
    Config(const Config&) = delete;
    Config& operator=(const Config&) = delete;

private:
    static std::unique_ptr<ConfigurationManager> instance_;
    static std::once_flag init_flag_;
    static std::mutex instance_mutex_;
};

/**
 * @brief Configuration file watcher for hot reloading
 * 
 * Monitors configuration files for changes and automatically reloads
 * configuration when files are modified.
 */
class ConfigurationWatcher {
public:
    /**
     * @brief Construct configuration watcher
     * @param config_manager Shared pointer to configuration manager to update
     */
    explicit ConfigurationWatcher(std::shared_ptr<ConfigurationManager> config_manager);
    
    /**
     * @brief Destructor - stops watching automatically
     */
    ~ConfigurationWatcher();
    
    /**
     * @brief Start watching configuration directory
     * @param directory_path Path to directory containing configuration files
     * @param check_interval_ms Check interval in milliseconds (default: 1000)
     */
    void start(const std::string& directory_path, int check_interval_ms = 1000);
    
    /**
     * @brief Stop watching configuration files
     */
    void stop();
    
    /**
     * @brief Check if watcher is currently running
     * @return bool True if watching is active
     */
    [[nodiscard]] bool is_watching() const noexcept;

private:
    void check_for_changes();
    
    std::shared_ptr<ConfigurationManager> config_manager_;
    mutable std::mutex mutex_;
    std::string directory_path_;
    int check_interval_ms_{1000};
    bool watching_{false};
    std::thread watcher_thread_;
    std::chrono::file_clock::time_point last_write_time_;
};

/**
 * @brief Configuration encryption/decryption utility
 * 
 * Provides AES-256 encryption for sensitive configuration values
 * such as passwords and API keys.
 */
class ConfigurationEncryptor {
public:
    /**
     * @brief Construct encryptor with encryption key
     * @param encryption_key Base64 encoded encryption key (32 bytes)
     */
    explicit ConfigurationEncryptor(const std::string& encryption_key);
    
    /**
     * @brief Destructor
     */
    ~ConfigurationEncryptor() = default;
    
    /**
     * @brief Encrypt a plaintext value
     * @param plaintext Plain text value to encrypt
     * @return std::string Encrypted value in ENC[AES256,data:...,iv:...] format
     */
    [[nodiscard]] std::string encrypt(const std::string& plaintext) const;
    
    /**
     * @brief Decrypt an encrypted value
     * @param encrypted_value Encrypted value in ENC[AES256,data:...,iv:...] format
     * @return std::string Decrypted plaintext value
     */
    [[nodiscard]] std::string decrypt(const std::string& encrypted_value) const;
    
    /**
     * @brief Check if a value is encrypted
     * @param value Value to check
     * @return bool True if value appears to be encrypted
     */
    [[nodiscard]] static bool is_encrypted(const std::string& value) noexcept;

private:
    std::string key_;
};

/**
 * @brief Configuration migration utility
 * 
 * Handles migration of configuration files between different versions
 * and provides backward compatibility support.
 */
class ConfigurationMigrator {
public:
    /**
     * @brief Construct migrator
     */
    ConfigurationMigrator() = default;
    
    /**
     * @brief Destructor
     */
    ~ConfigurationMigrator() = default;
    
    /**
     * @brief Migrate configuration file to target version
     * @param config_path Path to configuration file to migrate
     * @param target_version Target version to migrate to
     * @return std::shared_ptr<ConfigurationManager> Migrated configuration manager
     */
    [[nodiscard]] std::shared_ptr<ConfigurationManager> migrate(
        const std::string& config_path, 
        const std::string& target_version);
    
    /**
     * @brief Get current configuration version from file
     * @param config_path Path to configuration file
     * @return std::string Configuration version
     */
    [[nodiscard]] static std::string get_config_version(const std::string& config_path);
    
    /**
     * @brief Check if migration is needed
     * @param config_path Path to configuration file
     * @param target_version Target version
     * @return bool True if migration is needed
     */
    [[nodiscard]] static bool needs_migration(
        const std::string& config_path, 
        const std::string& target_version);

private:
    void migrate_v1_to_v2(YAML::Node& config);
    YAML::Node migrate_database_config(const YAML::Node& old_config);
};

} // namespace omop::common