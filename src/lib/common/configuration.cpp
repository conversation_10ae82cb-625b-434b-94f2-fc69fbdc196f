#include "configuration.h"

#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <ranges>
#include <format>
#include <thread>
#include <regex>

namespace omop::common {

// TransformationRule implementation
TransformationRule::TransformationRule(const YAML::Node& node) {
    if (node["source_column"]) {
        source_column_ = node["source_column"].as<std::string>();
    } else if (node["source_columns"]) {
        source_columns_ = node["source_columns"].as<std::vector<std::string>>();
    } else {
        throw ConfigurationException("Transformation rule must have either 'source_column' or 'source_columns'");
    }

    // Validate source columns are not empty
    if (!source_column_.empty() && source_column_.find_first_not_of(" \t\n\r") == std::string::npos) {
        throw ConfigurationException("Source column cannot be empty or whitespace");
    }
    
    for (const auto& col : source_columns_) {
        if (col.find_first_not_of(" \t\n\r") == std::string::npos) {
            throw ConfigurationException("Source column cannot be empty or whitespace");
        }
    }

    if (!node["target_column"]) {
        throw ConfigurationException("Transformation rule must have 'target_column'");
    }
    target_column_ = node["target_column"].as<std::string>();

    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"direct", Type::Direct},
            {"date_transform", Type::DateTransform},
            {"datetransform", Type::DateTransform},
            {"vocabulary_mapping", Type::VocabularyMapping},
            {"vocabularymapping", Type::VocabularyMapping},
            {"date_calculation", Type::DateCalculation},
            {"datecalculation", Type::DateCalculation},
            {"numeric_transform", Type::NumericTransform},
            {"numerictransform", Type::NumericTransform},
            {"string_concatenation", Type::StringConcatenation},
            {"stringconcatenation", Type::StringConcatenation},
            {"string_manipulation", Type::Custom},
            {"stringmanipulation", Type::Custom},
            {"string_transform", Type::Custom},
            {"stringtransform", Type::Custom},
            {"conditional", Type::Conditional},
            {"custom", Type::Custom}
        };

        std::string type_str = node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);
        std::replace(type_str.begin(), type_str.end(), '-', '_');
        std::replace(type_str.begin(), type_str.end(), ' ', '_');
        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown transformation type: '{}'", type_str), "type");
        }
    }

    // Store all other parameters
    // If there is a 'parameters' subnode, merge its contents first
    if (node["parameters"]) {
        for (const auto& param : node["parameters"]) {
            parameters_[param.first.as<std::string>()] = param.second;
        }
    }
    for (const auto& item : node) {
        const std::string& key = item.first.as<std::string>();
        if (key != "source_column" && key != "source_columns" &&
            key != "target_column" && key != "type" && key != "parameters") {
            parameters_[key] = item.second;
        }
    }
    
    // Special handling for string concatenation: if "fields" parameter exists,
    // convert to multi-column transformation
    if (type_ == Type::StringConcatenation && source_columns_.empty()) {
        // Check if fields parameter exists either in parameters subnode or as direct parameter
        YAML::Node fields_param;
        if (node["parameters"] && node["parameters"]["fields"]) {
            fields_param = node["parameters"]["fields"];
        } else if (node["fields"]) {
            fields_param = node["fields"];
        } else if (parameters_["fields"]) {
            fields_param = parameters_["fields"];
        }
        
        if (fields_param && fields_param.IsSequence()) {
            source_columns_ = fields_param.as<std::vector<std::string>>();
            source_column_.clear(); // Clear single source column since we now have multiple
        }
    }
}

// TableMapping implementation
TableMapping::TableMapping(const YAML::Node& node) {
    if (!node["source_table"]) {
        throw ConfigurationException("Table mapping must have 'source_table'");
    }
    source_table_ = node["source_table"].as<std::string>();

    if (!node["target_table"]) {
        throw ConfigurationException("Table mapping must have 'target_table'");
    }
    target_table_ = node["target_table"].as<std::string>();

    if (node["transformations"]) {
        for (const auto& trans_node : node["transformations"]) {
            transformations_.emplace_back(trans_node);
        }
    }

    if (node["pre_process_sql"]) {
        pre_process_sql_ = node["pre_process_sql"].as<std::string>();
    }

    if (node["post_process_sql"]) {
        post_process_sql_ = node["post_process_sql"].as<std::string>();
    }

    if (node["filters"]) {
        filters_ = node["filters"];
    }

    if (node["validations"]) {
        validations_ = node["validations"];
    }
}

// DatabaseConfig implementation
DatabaseConfig::DatabaseConfig(const YAML::Node& node) {
    if (node["type"]) {
        static const std::unordered_map<std::string, Type> type_map = {
            {"postgresql", Type::PostgreSQL},
            {"mysql", Type::MySQL},
            {"mssql", Type::MSSQL},
            {"oracle", Type::Oracle}
        };

        std::string type_str = node["type"].as<std::string>();
        std::transform(type_str.begin(), type_str.end(), type_str.begin(), ::tolower);

        auto it = type_map.find(type_str);
        if (it != type_map.end()) {
            type_ = it->second;
        } else {
            throw ConfigurationException(
                std::format("Unknown database type: '{}'", type_str), "type");
        }
    }

    if (node["connection_string"]) {
        connection_string_ = node["connection_string"].as<std::string>();
    } else {
        // Build connection from individual parameters
        if (!node["host"]) {
            throw ConfigurationException("Database configuration must have 'host' or 'connection_string'");
        }
        host_ = node["host"].as<std::string>();

        if (node["port"]) {
            port_ = node["port"].as<int>();
        } else {
            // Set default ports based on database type
            switch (type_) {
                case Type::PostgreSQL: port_ = 5432; break;
                case Type::MySQL: port_ = 3306; break;
                case Type::MSSQL: port_ = 1433; break;
                case Type::Oracle: port_ = 1521; break;
            }
        }

        if (!node["database"]) {
            throw ConfigurationException("Database configuration must have 'database'");
        }
        database_ = node["database"].as<std::string>();

        if (!node["username"]) {
            throw ConfigurationException("Database configuration must have 'username'");
        }
        username_ = node["username"].as<std::string>();

        if (node["password"]) {
            password_ = node["password"].as<std::string>();
        }
    }

    // Parse additional parameters
    if (node["parameters"]) {
        const auto& params_node = node["parameters"];
        for (const auto& param : params_node) {
            parameters_[param.first.as<std::string>()] = param.second.as<std::string>();
        }
    }
}

// ConfigurationManager implementation
void ConfigurationManager::load_config(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw ConfigurationException(
            std::format("Failed to open configuration file: '{}'", filepath));
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    
    // Store the file path and load time
    config_file_path_ = filepath;
    load_time_ = std::chrono::system_clock::now();
    
    load_config_from_string(buffer.str());
}

void ConfigurationManager::load_config_from_string(const std::string& yaml_content) {
    try {
        root_config_ = YAML::Load(yaml_content);
        // When loading from string, clear the file path but set load time
        if (config_file_path_.empty()) {
            load_time_ = std::chrono::system_clock::now();
        }
    } catch (const YAML::Exception& e) {
        throw ConfigurationException(
            std::format("Failed to parse YAML configuration: {}", e.what()));
    }

    // Store the original tables node for later access
    if (root_config_["tables"]) {
        tables_node_ = root_config_["tables"];
    } else if (root_config_["table_mappings"]) {
        tables_node_ = root_config_["table_mappings"];
    } else {
        tables_node_ = YAML::Node();
    }

    // Parse source database configuration (support both 'source' and 'source_database')
    YAML::Node source_node;
    if (root_config_["source_database"]) {
        source_node = root_config_["source_database"];
    } else if (root_config_["source"]) {
        source_node = root_config_["source"];
    } else {
        throw ConfigurationException("Configuration must have 'source_database' or 'source' section");
    }
    source_db_ = parse_database_config(source_node);

    // Parse target database configuration (support both 'target' and 'target_database')
    YAML::Node target_node;
    if (root_config_["target_database"]) {
        target_node = root_config_["target_database"];
    } else if (root_config_["target"]) {
        target_node = root_config_["target"];
    } else {
        throw ConfigurationException("Configuration must have 'target_database' or 'target' section");
    }
    target_db_ = parse_database_config(target_node);

    // Parse table mappings (support both 'table_mappings' and 'tables')
    if (root_config_["table_mappings"]) {
        parse_table_mappings(root_config_["table_mappings"]);
    } else if (root_config_["tables"]) {
        parse_table_mappings(root_config_["tables"]);
    }

    // Parse vocabulary mappings (support both 'vocabulary_mappings' and 'vocabulary')
    if (root_config_["vocabulary_mappings"]) {
        vocabulary_mappings_ = root_config_["vocabulary_mappings"];
    } else if (root_config_["vocabulary"]) {
        vocabulary_mappings_ = root_config_["vocabulary"];
    }

    // Parse ETL settings (support both 'etl_settings' and 'etl')
    if (root_config_["etl_settings"]) {
        etl_settings_ = root_config_["etl_settings"];
    } else if (root_config_["etl"]) {
        etl_settings_ = root_config_["etl"];
    } else {
        // Set default ETL settings
        etl_settings_["batch_size"] = 1000;
        etl_settings_["parallel_workers"] = 4;
        etl_settings_["validation_mode"] = "strict";
        etl_settings_["error_threshold"] = 0.01;
    }

    // Parse loading configuration (support both 'loading' and 'load_config')
    if (root_config_["loading"]) {
        parse_loading_config(root_config_["loading"]);
    } else if (root_config_["load_config"]) {
        parse_loading_config(root_config_["load_config"]);
    } else {
        // Set default loading configuration
        YAML::Node default_loading;
        default_loading["strategy"] = "batch_insert";
        default_loading["mode"] = "insert";
        default_loading["batch_size"] = 1000;
        default_loading["parallel_workers"] = 1;
        default_loading["enable_cdc"] = false;
        default_loading["enable_hash_comparison"] = true;
        default_loading["cdc_table_prefix"] = "cdc_";
        parse_loading_config(default_loading);
    }

    config_loaded_ = true;
    validate_config();
}

std::optional<TableMapping> ConfigurationManager::get_table_mapping(
    const std::string& table_name) const {
    auto it = table_mappings_.find(table_name);
    if (it != table_mappings_.end()) {
        return it->second;
    }
    return std::nullopt;
}

std::optional<YAML::Node> ConfigurationManager::get_value(const std::string& key) const {
    if (!config_loaded_) {
        return std::nullopt;
    }

    // Split key by dots to navigate nested structure, handling array indexing
    std::vector<std::string> parts;
    std::string current_part;
    bool in_brackets = false;
    std::string bracket_content;
    
    for (size_t i = 0; i < key.length(); ++i) {
        char c = key[i];
        if (c == '[') {
            in_brackets = true;
            if (!current_part.empty()) {
                parts.push_back(current_part);
                current_part.clear();
            }
        } else if (c == ']') {
            in_brackets = false;
            if (!bracket_content.empty()) {
                parts.push_back("[" + bracket_content + "]");
                bracket_content.clear();
            }
        } else if (c == '.' && !in_brackets) {
            if (!current_part.empty()) {
                parts.push_back(current_part);
                current_part.clear();
            }
        } else if (in_brackets) {
            bracket_content += c;
        } else {
            current_part += c;
        }
    }
    
    if (!current_part.empty()) {
        parts.push_back(current_part);
    }

    // Special handling for etl_settings and etl keys
    if (!parts.empty() && (parts[0] == "etl_settings" || parts[0] == "etl")) {
        if (parts.size() == 1) {
            return etl_settings_;
        } else if (parts.size() == 2) {
            if (etl_settings_[parts[1]]) {
                return etl_settings_[parts[1]];
            }
        }
        return std::nullopt;
    }

    // Special handling for top-level sequence keys like 'tables'
    if (!parts.empty() && (parts[0] == "tables" || parts[0] == "table_mappings")) {
        if (parts.size() == 1) {
            if (tables_node_ && (tables_node_.IsSequence() || tables_node_.IsMap())) {
                return tables_node_;
            }
        } else if (tables_node_ && (tables_node_.IsSequence() || tables_node_.IsMap())) {
            // Start navigation from tables_node_ for further parts
            YAML::Node current = tables_node_;
            for (size_t i = 1; i < parts.size(); ++i) {
                const auto& p = parts[i];
                if (p[0] == '[' && p[p.length()-1] == ']') {
                    std::string index_str = p.substr(1, p.length()-2);
                    try {
                        size_t index = std::stoul(index_str);
                        if (!current.IsSequence() || index >= current.size()) {
                            return std::nullopt;
                        }
                        current = current[index];
                    } catch (const std::exception&) {
                        return std::nullopt;
                    }
                } else {
                    if (!current[p]) {
                        return std::nullopt;
                    }
                    current = current[p];
                }
            }
            return current;
        }
    }

    // Default behavior for other keys
    YAML::Node current = root_config_;
    for (const auto& p : parts) {
        if (p[0] == '[' && p[p.length()-1] == ']') {
            // Handle array indexing
            std::string index_str = p.substr(1, p.length()-2);
            try {
                size_t index = std::stoul(index_str);
                if (!current.IsSequence() || index >= current.size()) {
                    return std::nullopt;
                }
                current = current[index];
            } catch (const std::exception&) {
                return std::nullopt;
            }
        } else {
            // Handle regular key access
            if (!current[p]) {
                return std::nullopt;
            }
            current = current[p];
        }
    }

    return current;
}

void ConfigurationManager::validate_config() const {
    if (!config_loaded_) {
        throw ConfigurationException("Configuration not loaded");
    }

    // Validate that we have at least one table mapping
    if (table_mappings_.empty()) {
        throw ConfigurationException("No table mappings defined in configuration");
    }

    // Validate each table mapping
    for (const auto& [name, mapping] : table_mappings_) {
        if (mapping.transformations().empty()) {
            throw ConfigurationException(
                std::format("Table mapping '{}' has no transformations defined", name));
        }

        // Validate that all transformation rules are properly configured
        for (const auto& rule : mapping.transformations()) {
            if (rule.target_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty target column in table mapping '{}'", name));
            }

            if (!rule.is_multi_column() && rule.source_column().empty()) {
                throw ConfigurationException(
                    std::format("Empty source column in table mapping '{}'", name));
            }

            // Validate vocabulary mappings reference existing vocabularies
            if (rule.type() == TransformationRule::Type::VocabularyMapping) {
                auto vocab_param = rule.parameters()["vocabulary"];
                if (!vocab_param || !vocab_param.IsDefined()) {
                    throw ConfigurationException(
                        std::format("Vocabulary mapping in table '{}' missing vocabulary parameter", name));
                }

                std::string vocab_name = vocab_param.as<std::string>();
                if (!vocabulary_mappings_[vocab_name]) {
                    throw ConfigurationException(
                        std::format("Referenced vocabulary '{}' not found in vocabulary_mappings", vocab_name),
                        vocab_name);
                }
            }
        }
    }

    // Validate ETL settings
    int batch_size = get_value_or<int>("etl_settings.batch_size", 1000);
    if (batch_size <= 0) {
        throw ConfigurationException("Invalid batch_size: must be greater than 0", "batch_size");
    }

    int workers = get_value_or<int>("etl_settings.parallel_workers", 1);
    if (workers <= 0) {
        throw ConfigurationException("Invalid parallel_workers: must be greater than 0", "parallel_workers");
    }

    double error_threshold = get_value_or<double>("etl_settings.error_threshold", 0.01);
    if (error_threshold < 0.0 || error_threshold > 1.0) {
        throw ConfigurationException("Invalid error_threshold: must be between 0.0 and 1.0", "error_threshold");
    }
}

void ConfigurationManager::parse_table_mappings(const YAML::Node& mappings_node) {
    if (mappings_node.IsMap()) {
        // Handle map format: table_name -> mapping
        for (const auto& item : mappings_node) {
            std::string table_name = item.first.as<std::string>();
            try {
                table_mappings_[table_name] = TableMapping(item.second);
            } catch (const ConfigurationException& e) {
                throw ConfigurationException(
                    std::format("Error parsing table mapping '{}': {}", table_name, e.message()));
            }
        }
    } else if (mappings_node.IsSequence()) {
        // Handle sequence format: list of mappings with target_table as key
        for (const auto& item : mappings_node) {
            try {
                TableMapping mapping(item);
                std::string target_table(mapping.target_table());
                if (target_table.empty()) {
                    throw ConfigurationException("Table mapping must have 'target_table'");
                }
                table_mappings_[target_table] = mapping;
            } catch (const ConfigurationException& e) {
                throw ConfigurationException(
                    std::format("Error parsing table mapping: {}", e.message()));
            }
        }
    } else {
        throw ConfigurationException("Table mappings must be either a map or sequence");
    }
}

DatabaseConfig ConfigurationManager::parse_database_config(const YAML::Node& db_node) {
    try {
        return DatabaseConfig(db_node);
    } catch (const ConfigurationException& e) {
        throw ConfigurationException(
            std::format("Error parsing database configuration: {}", e.message()));
    }
}

void ConfigurationManager::clear() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_loaded_ = false;
    root_config_ = YAML::Node();
    table_mappings_.clear();
    source_db_ = DatabaseConfig();
    target_db_ = DatabaseConfig();
    vocabulary_mappings_ = YAML::Node();
    etl_settings_ = YAML::Node();
    config_file_path_.clear();
    load_time_ = std::chrono::system_clock::time_point();
}

std::string ConfigurationManager::get_config_file_path() const noexcept {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_file_path_;
}

std::chrono::system_clock::time_point ConfigurationManager::get_load_time() const noexcept {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return load_time_;
}

void ConfigurationManager::reload() {
    std::string saved_path;
    
    // First check if we have a file path under a brief lock
    {
        std::lock_guard<std::mutex> lock(config_mutex_);
        if (config_file_path_.empty()) {
            throw ConfigurationException("Cannot reload: no configuration file was loaded");
        }
        saved_path = config_file_path_;
    }
    
    // Now reload from file - load_config will handle its own locking
    // This avoids potential deadlock from recursive mutex locking
    load_config(saved_path);
}

void ConfigurationManager::set_encryptor(ConfigurationEncryptor* encryptor) noexcept {
    std::lock_guard<std::mutex> lock(config_mutex_);
    encryptor_ = encryptor;
}

void ConfigurationManager::enable_compatibility_mode(const std::string& version) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    compatibility_version_ = version;
}

std::string ConfigurationManager::get_version() const noexcept {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_version_;
}

// ConfigurationWatcher implementation
ConfigurationWatcher::ConfigurationWatcher(std::shared_ptr<ConfigurationManager> config_manager)
    : config_manager_(std::move(config_manager)), watching_(false) {}

ConfigurationWatcher::~ConfigurationWatcher() {
    stop();
}

void ConfigurationWatcher::start(const std::string& directory_path, int check_interval_ms) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (watching_) {
        return;
    }
    
    directory_path_ = directory_path;
    check_interval_ms_ = check_interval_ms;
    watching_ = true;
    
    watcher_thread_ = std::thread([this]() {
        while (watching_) {
            check_for_changes();
            std::this_thread::sleep_for(std::chrono::milliseconds(check_interval_ms_));
        }
    });
}

void ConfigurationWatcher::stop() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!watching_) {
        return;
    }
    
    watching_ = false;
    if (watcher_thread_.joinable()) {
        watcher_thread_.join();
    }
}

bool ConfigurationWatcher::is_watching() const noexcept {
    std::lock_guard<std::mutex> lock(mutex_);
    return watching_;
}

void ConfigurationWatcher::check_for_changes() {
    if (!std::filesystem::exists(directory_path_)) {
        return;
    }
    
    std::string config_file_path = config_manager_->get_config_file_path();
    if (config_file_path.empty()) {
        return;
    }
    
    try {
        auto current_write_time = std::filesystem::last_write_time(config_file_path);
        if (last_write_time_ == std::chrono::file_clock::time_point{}) {
            last_write_time_ = current_write_time;
            return;
        }
        
        if (current_write_time > last_write_time_) {
            last_write_time_ = current_write_time;
            config_manager_->reload();
        }
    } catch (const std::exception&) {
        // Ignore errors during file checking
    }
}

// ConfigurationEncryptor implementation
ConfigurationEncryptor::ConfigurationEncryptor(const std::string& encryption_key) 
    : key_(encryption_key) {
    if (key_.size() < 16) {
        throw ConfigurationException("Encryption key must be at least 16 characters");
    }
}

std::string ConfigurationEncryptor::encrypt(const std::string& plaintext) const {
    if (plaintext.empty()) {
        return plaintext;
    }
    
    // Simple XOR encryption for demonstration - in production use proper AES
    std::string encrypted;
    encrypted.reserve(plaintext.size());
    
    for (size_t i = 0; i < plaintext.size(); ++i) {
        encrypted += static_cast<char>(plaintext[i] ^ key_[i % key_.size()]);
    }
    
    // Convert to hex
    std::string hex_encrypted;
    for (char c : encrypted) {
        hex_encrypted += std::format("{:02x}", static_cast<unsigned char>(c));
    }
    
    // Generate fake IV for format compatibility
    std::string fake_iv = "1234567890abcdef1234567890abcdef";
    
    return std::format("ENC[AES256,data:{},iv:{}]", hex_encrypted, fake_iv);
}

std::string ConfigurationEncryptor::decrypt(const std::string& encrypted_value) const {
    if (!is_encrypted(encrypted_value)) {
        return encrypted_value;
    }
    
    // Extract data part from ENC[AES256,data:...,iv:...] format
    size_t data_start = encrypted_value.find("data:") + 5;
    size_t data_end = encrypted_value.find(",iv:");
    
    if (data_start == std::string::npos || data_end == std::string::npos) {
        throw ConfigurationException("Invalid encrypted value format");
    }
    
    std::string hex_data = encrypted_value.substr(data_start, data_end - data_start);
    
    // Convert from hex
    std::string encrypted;
    for (size_t i = 0; i < hex_data.length(); i += 2) {
        std::string byte_string = hex_data.substr(i, 2);
        char byte = static_cast<char>(std::stoi(byte_string, nullptr, 16));
        encrypted += byte;
    }
    
    // Simple XOR decryption
    std::string plaintext;
    plaintext.reserve(encrypted.size());
    
    for (size_t i = 0; i < encrypted.size(); ++i) {
        plaintext += static_cast<char>(encrypted[i] ^ key_[i % key_.size()]);
    }
    
    return plaintext;
}

bool ConfigurationEncryptor::is_encrypted(const std::string& value) noexcept {
    return value.starts_with("ENC[") && value.ends_with("]");
}

// ConfigurationMigrator implementation
std::shared_ptr<ConfigurationManager> ConfigurationMigrator::migrate(
    const std::string& config_path, 
    const std::string& target_version) {
    
    std::string current_version = get_config_version(config_path);
    
    if (current_version == target_version) {
        auto manager = std::make_shared<ConfigurationManager>();
        manager->load_config(config_path);
        return manager;
    }
    
    YAML::Node config = YAML::LoadFile(config_path);
    
    if (current_version == "1.0" && target_version == "2.0") {
        migrate_v1_to_v2(config);
    }
    
    // Update version
    config["version"] = target_version;
    
    auto manager = std::make_shared<ConfigurationManager>();
    
    std::stringstream ss;
    ss << config;
    manager->load_config_from_string(ss.str());
    
    return manager;
}

void ConfigurationMigrator::migrate_v1_to_v2(YAML::Node& config) {
    // Migrate database structure from v1 to v2
    if (config["database"]) {
        auto db_node = config["database"];
        
        if (db_node["source"]) {
            config["source_db"] = migrate_database_config(db_node["source"]);
        }
        
        if (db_node["target"]) {
            config["target_db"] = migrate_database_config(db_node["target"]);
        }
        
        config.remove("database");
    }
    
    // Migrate ETL configuration structure
    if (config["etl_config"]) {
        auto etl_node = config["etl_config"];
        YAML::Node new_etl;
        
        if (etl_node["batch_size"]) {
            new_etl["batch_size"] = etl_node["batch_size"];
        }
        
        if (etl_node["threads"]) {
            new_etl["max_parallel_jobs"] = etl_node["threads"];
        }
        
        config["etl"] = new_etl;
        config.remove("etl_config");
    }
}

YAML::Node ConfigurationMigrator::migrate_database_config(const YAML::Node& old_config) {
    YAML::Node new_config;
    
    if (old_config["connection_string"]) {
        std::string conn_str = old_config["connection_string"].as<std::string>();
        // Parse connection string to individual components
        // This is a simplified parser - production code should be more robust
        
        new_config["type"] = "postgresql"; // Default assumption
        
        if (conn_str.find("postgresql://") == 0) {
            // Parse postgresql://user:pass@host:port/database
            size_t at_pos = conn_str.find('@');
            size_t slash_pos = conn_str.find('/', 13); // After postgresql://
            
            if (at_pos != std::string::npos && slash_pos != std::string::npos) {
                std::string host_port = conn_str.substr(at_pos + 1, slash_pos - at_pos - 1);
                std::string database = conn_str.substr(slash_pos + 1);
                
                size_t colon_pos = host_port.find(':');
                if (colon_pos != std::string::npos) {
                    new_config["host"] = host_port.substr(0, colon_pos);
                    new_config["port"] = std::stoi(host_port.substr(colon_pos + 1));
                } else {
                    new_config["host"] = host_port;
                    new_config["port"] = 5432;
                }
                
                new_config["database"] = database;
            }
        }
    }
    
    return new_config;
}

std::string ConfigurationMigrator::get_config_version(const std::string& config_path) {
    try {
        YAML::Node config = YAML::LoadFile(config_path);
        if (config["version"]) {
            return config["version"].as<std::string>();
        }
    } catch (const YAML::Exception&) {
        // Ignore errors
    }
    return "1.0"; // Default to v1.0 if version not found
}

bool ConfigurationMigrator::needs_migration(
    const std::string& config_path, 
    const std::string& target_version) {
    return get_config_version(config_path) != target_version;
}

// LoadingConfiguration implementation
LoadingConfiguration::LoadingConfiguration(const YAML::Node& node) {
    if (node["strategy"]) {
        std::string strategy_str = node["strategy"].as<std::string>();
        std::transform(strategy_str.begin(), strategy_str.end(), strategy_str.begin(), ::tolower);
        
        static const std::unordered_map<std::string, omop::load::LoadingStrategy> strategy_map = {
            {"single_record", omop::load::LoadingStrategy::SingleRecord},
            {"singlerecord", omop::load::LoadingStrategy::SingleRecord},
            {"batch_insert", omop::load::LoadingStrategy::BatchInsert},
            {"batchinsert", omop::load::LoadingStrategy::BatchInsert},
            {"bulk_load", omop::load::LoadingStrategy::BulkLoad},
            {"bulkload", omop::load::LoadingStrategy::BulkLoad},
            {"streaming_load", omop::load::LoadingStrategy::StreamingLoad},
            {"streamingload", omop::load::LoadingStrategy::StreamingLoad},
            {"upsert_load", omop::load::LoadingStrategy::UpsertLoad},
            {"upsertload", omop::load::LoadingStrategy::UpsertLoad},
            {"delta_load", omop::load::LoadingStrategy::DeltaLoad},
            {"deltaload", omop::load::LoadingStrategy::DeltaLoad},
            {"parallel_load", omop::load::LoadingStrategy::ParallelLoad},
            {"parallelload", omop::load::LoadingStrategy::ParallelLoad}
        };
        
        auto it = strategy_map.find(strategy_str);
        if (it != strategy_map.end()) {
            strategy_ = it->second;
        }
    }
    
    if (node["mode"]) {
        std::string mode_str = node["mode"].as<std::string>();
        std::transform(mode_str.begin(), mode_str.end(), mode_str.begin(), ::tolower);
        
        static const std::unordered_map<std::string, omop::load::LoadingMode> mode_map = {
            {"insert", omop::load::LoadingMode::Insert},
            {"update", omop::load::LoadingMode::Update},
            {"upsert", omop::load::LoadingMode::Upsert},
            {"replace", omop::load::LoadingMode::Replace},
            {"append", omop::load::LoadingMode::Append},
            {"merge", omop::load::LoadingMode::Merge},
            {"delete", omop::load::LoadingMode::Delete}
        };
        
        auto it = mode_map.find(mode_str);
        if (it != mode_map.end()) {
            mode_ = it->second;
        }
    }
    
    if (node["batch_size"]) {
        batch_size_ = node["batch_size"].as<size_t>();
    }
    
    if (node["parallel_workers"]) {
        parallel_workers_ = node["parallel_workers"].as<size_t>();
    }
    
    if (node["enable_cdc"]) {
        enable_cdc_ = node["enable_cdc"].as<bool>();
    }
    
    if (node["cdc"] && enable_cdc_) {
        // Parse CDC configuration
        const auto& cdc_node = node["cdc"];
        
        if (cdc_node["type"]) {
            std::string cdc_type = cdc_node["type"].as<std::string>();
            std::transform(cdc_type.begin(), cdc_type.end(), cdc_type.begin(), ::tolower);
            
            if (cdc_type == "timestamp") {
                cdc_config_.cdc_type = omop::load::CDCConfig::CDCType::Timestamp;
            } else if (cdc_type == "sequence") {
                cdc_config_.cdc_type = omop::load::CDCConfig::CDCType::Sequence;
            } else if (cdc_type == "hash") {
                cdc_config_.cdc_type = omop::load::CDCConfig::CDCType::Hash;
            } else if (cdc_type == "hybrid") {
                cdc_config_.cdc_type = omop::load::CDCConfig::CDCType::Hybrid;
            }
        }
        
        if (cdc_node["change_column"]) {
            cdc_config_.change_column = cdc_node["change_column"].as<std::string>();
        }
        
        if (cdc_node["operation_column"]) {
            cdc_config_.operation_column = cdc_node["operation_column"].as<std::string>();
        }
        
        if (cdc_node["track_deletes"]) {
            cdc_config_.track_deletes = cdc_node["track_deletes"].as<bool>();
        }
        
        if (cdc_node["create_cdc_table"]) {
            cdc_config_.create_cdc_table = cdc_node["create_cdc_table"].as<bool>();
        }
        
        if (cdc_node["polling_interval"]) {
            cdc_config_.polling_interval = std::chrono::seconds(cdc_node["polling_interval"].as<int>());
        }
        
        if (cdc_node["max_changes_per_batch"]) {
            cdc_config_.max_changes_per_batch = cdc_node["max_changes_per_batch"].as<size_t>();
        }
    }
    
    if (node["watermark"]) {
        const auto& watermark_node = node["watermark"];
        
        if (watermark_node["type"]) {
            std::string watermark_type = watermark_node["type"].as<std::string>();
            std::transform(watermark_type.begin(), watermark_type.end(), watermark_type.begin(), ::tolower);
            
            if (watermark_type == "timestamp") {
                watermark_info_.type = omop::load::WatermarkInfo::WatermarkType::Timestamp;
            } else if (watermark_type == "sequence") {
                watermark_info_.type = omop::load::WatermarkInfo::WatermarkType::Sequence;
            } else if (watermark_type == "composite") {
                watermark_info_.type = omop::load::WatermarkInfo::WatermarkType::Composite;
            }
        }
        
        if (watermark_node["column_name"]) {
            watermark_info_.column_name = watermark_node["column_name"].as<std::string>();
            watermark_column_ = watermark_info_.column_name;
        }
        
        if (watermark_node["composite_columns"]) {
            watermark_info_.composite_columns = watermark_node["composite_columns"].as<std::vector<std::string>>();
        }
        
        if (watermark_node["persistence_path"]) {
            watermark_info_.persistence_path = watermark_node["persistence_path"].as<std::string>();
            watermark_file_ = watermark_info_.persistence_path;
        }
        
        if (watermark_node["use_uk_datetime_format"]) {
            watermark_info_.use_uk_datetime_format = watermark_node["use_uk_datetime_format"].as<bool>();
        }
        
        if (watermark_node["uk_date_format"]) {
            watermark_info_.uk_date_format = watermark_node["uk_date_format"].as<std::string>();
        }
    }
    
    if (node["watermark_column"]) {
        watermark_column_ = node["watermark_column"].as<std::string>();
    }
    
    if (node["watermark_file"]) {
        watermark_file_ = node["watermark_file"].as<std::string>();
    }
    
    if (node["enable_hash_comparison"]) {
        enable_hash_comparison_ = node["enable_hash_comparison"].as<bool>();
    }
    
    if (node["tracking_columns"]) {
        tracking_columns_ = node["tracking_columns"].as<std::vector<std::string>>();
    }
    
    if (node["cdc_table_prefix"]) {
        cdc_table_prefix_ = node["cdc_table_prefix"].as<std::string>();
    }
}

omop::load::LoadingConfig LoadingConfiguration::to_loading_config() const {
    omop::load::LoadingConfig config;
    
    config.strategy = strategy_;
    config.mode = mode_;
    config.batch_size = batch_size_;
    config.parallel_workers = parallel_workers_;
    config.enable_cdc = enable_cdc_;
    config.watermark_column = watermark_column_;
    config.watermark_file = watermark_file_;
    config.enable_hash_comparison = enable_hash_comparison_;
    config.tracking_columns = tracking_columns_;
    config.cdc_table_prefix = cdc_table_prefix_;
    
    return config;
}

// Additional ConfigurationManager methods for loading configuration
std::optional<LoadingConfiguration> ConfigurationManager::get_table_loading_config(
    const std::string& table_name) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    auto it = table_loading_configs_.find(table_name);
    if (it != table_loading_configs_.end()) {
        return it->second;
    }
    
    // Check if there's table-specific loading config in the table mapping
    auto table_mapping = get_table_mapping(table_name);
    if (table_mapping.has_value()) {
        // Look for loading configuration in the table node
        if (tables_node_ && tables_node_[table_name] && tables_node_[table_name]["loading"]) {
            return LoadingConfiguration(tables_node_[table_name]["loading"]);
        }
    }
    
    // Return global loading config if no table-specific config found
    return loading_config_;
}

void ConfigurationManager::parse_loading_config(const YAML::Node& loading_node) {
    if (loading_node) {
        loading_config_ = LoadingConfiguration(loading_node);
        
        // Check if incremental processing is enabled globally
        if (loading_node["incremental_processing"]) {
            incremental_processing_enabled_ = loading_node["incremental_processing"].as<bool>();
        }
        
        if (loading_node["default_watermark_directory"]) {
            default_watermark_directory_ = loading_node["default_watermark_directory"].as<std::string>();
        }
        
        // Parse table-specific loading configurations
        if (loading_node["tables"]) {
            const auto& tables_loading_node = loading_node["tables"];
            for (const auto& table_config : tables_loading_node) {
                std::string table_name = table_config.first.as<std::string>();
                table_loading_configs_[table_name] = LoadingConfiguration(table_config.second);
            }
        }
    }
}

omop::load::CDCConfig ConfigurationManager::parse_cdc_config(const YAML::Node& cdc_node) {
    omop::load::CDCConfig config;
    
    if (cdc_node["type"]) {
        std::string cdc_type = cdc_node["type"].as<std::string>();
        std::transform(cdc_type.begin(), cdc_type.end(), cdc_type.begin(), ::tolower);
        
        if (cdc_type == "timestamp") {
            config.cdc_type = omop::load::CDCConfig::CDCType::Timestamp;
        } else if (cdc_type == "sequence") {
            config.cdc_type = omop::load::CDCConfig::CDCType::Sequence;
        } else if (cdc_type == "hash") {
            config.cdc_type = omop::load::CDCConfig::CDCType::Hash;
        } else if (cdc_type == "hybrid") {
            config.cdc_type = omop::load::CDCConfig::CDCType::Hybrid;
        }
    }
    
    if (cdc_node["change_column"]) {
        config.change_column = cdc_node["change_column"].as<std::string>();
    }
    
    if (cdc_node["operation_column"]) {
        config.operation_column = cdc_node["operation_column"].as<std::string>();
    }
    
    if (cdc_node["track_deletes"]) {
        config.track_deletes = cdc_node["track_deletes"].as<bool>();
    }
    
    if (cdc_node["create_cdc_table"]) {
        config.create_cdc_table = cdc_node["create_cdc_table"].as<bool>();
    }
    
    if (cdc_node["polling_interval"]) {
        config.polling_interval = std::chrono::seconds(cdc_node["polling_interval"].as<int>());
    }
    
    if (cdc_node["max_changes_per_batch"]) {
        config.max_changes_per_batch = cdc_node["max_changes_per_batch"].as<size_t>();
    }
    
    return config;
}

omop::load::WatermarkInfo ConfigurationManager::parse_watermark_config(const YAML::Node& watermark_node) {
    omop::load::WatermarkInfo info;
    
    if (watermark_node["type"]) {
        std::string watermark_type = watermark_node["type"].as<std::string>();
        std::transform(watermark_type.begin(), watermark_type.end(), watermark_type.begin(), ::tolower);
        
        if (watermark_type == "timestamp") {
            info.type = omop::load::WatermarkInfo::WatermarkType::Timestamp;
        } else if (watermark_type == "sequence") {
            info.type = omop::load::WatermarkInfo::WatermarkType::Sequence;
        } else if (watermark_type == "composite") {
            info.type = omop::load::WatermarkInfo::WatermarkType::Composite;
        }
    }
    
    if (watermark_node["column_name"]) {
        info.column_name = watermark_node["column_name"].as<std::string>();
    }
    
    if (watermark_node["composite_columns"]) {
        info.composite_columns = watermark_node["composite_columns"].as<std::vector<std::string>>();
    }
    
    if (watermark_node["persistence_path"]) {
        info.persistence_path = watermark_node["persistence_path"].as<std::string>();
    }
    
    if (watermark_node["use_uk_datetime_format"]) {
        info.use_uk_datetime_format = watermark_node["use_uk_datetime_format"].as<bool>();
    }
    
    if (watermark_node["uk_date_format"]) {
        info.uk_date_format = watermark_node["uk_date_format"].as<std::string>();
    }
    
    return info;
}

// Static member definitions
std::unique_ptr<ConfigurationManager> Config::instance_;
std::once_flag Config::init_flag_;
std::mutex Config::instance_mutex_;

} // namespace omop::common