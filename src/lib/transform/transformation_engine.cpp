#include "transform/transformation_engine.h"
#include "transform/transformations.h"
#include "transform/numeric_transformations.h"
#include "transform/date_transformations.h"
#include "transform/conditional_transformations.h"
#include "transform/string_transformations.h"
#include "transform/unified_string_transformation.h"
#include "transform/anonymization_transformations.h"
#include "transform/vocabulary_service.h"
#include "common/logging.h"
#include "common/validation.h"
#include <regex>
#include <iomanip>
#include <sstream>
#include <chrono>
#include <atomic>

namespace omop::transform {

// Date Transformation Implementation
std::any DateTransformation::transform(const std::any& input,
                                      core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-etl-transform");
    logger->debug("[DateTransformation] Called with input type: {}", input.type().name());
    if (!input.has_value()) {
        logger->debug("[DateTransformation] Input is empty");
        return std::any{};
    }
    
    try {
        std::tm tm = {};
        
        if (input.type() == typeid(std::string)) {
            std::string date_str = std::any_cast<std::string>(input);
            logger->debug("[DateTransformation] Input string: {}", date_str);
            
            // Return empty result for empty strings
            if (date_str.empty()) {
                logger->debug("[DateTransformation] Input string is empty");
                return std::any{};
            }
            
            // Try multiple UK-compatible formats
            std::vector<std::string> formats = {input_format_};
            
            // Add UK alternative formats if not already included
            if (input_format_ == "%d/%m/%Y") {
                formats.push_back("%d.%m.%Y");  // Dot separator
                formats.push_back("%d-%m-%Y");  // Dash separator
            }
            
            bool parsed_successfully = false;
            for (const auto& format : formats) {
                std::istringstream iss(date_str);
                std::tm temp_tm = {};
                iss >> std::get_time(&temp_tm, format.c_str());
                if (!iss.fail()) {
                    tm = temp_tm;
                    parsed_successfully = true;
                    break;
                }
            }
            
            if (!parsed_successfully) {
                throw common::TransformationException(
                    std::format("Failed to parse date '{}' with any supported format (tried {} formats)", date_str, formats.size()),
                    "date_field", "date_transform");
            }
            
            // Additional validation for invalid dates that std::get_time might accept
            // Check for obviously invalid dates
            if (tm.tm_mon < 0 || tm.tm_mon > 11 ||  // Invalid month
                tm.tm_mday < 1 || tm.tm_mday > 31 ||  // Invalid day
                tm.tm_year < 0) {  // Invalid year
                throw common::TransformationException(
                    std::format("Invalid date values parsed from '{}' with format '{}'", date_str, input_format_),
                    "date_field", "date_transform");
            }
            
            // Check for specific invalid day/month combinations
            if ((tm.tm_mon == 1 && tm.tm_mday > 29) ||  // February with more than 29 days
                ((tm.tm_mon == 3 || tm.tm_mon == 5 || tm.tm_mon == 8 || tm.tm_mon == 10) && tm.tm_mday > 30) ||  // April, June, September, November with 31 days
                (tm.tm_mon == 1 && tm.tm_mday == 29 && !((tm.tm_year + 1900) % 4 == 0 && ((tm.tm_year + 1900) % 100 != 0 || (tm.tm_year + 1900) % 400 == 0)))) {  // Feb 29 on non-leap year
                throw common::TransformationException(
                    std::format("Invalid date '{}' - day {} is not valid for month {}", date_str, tm.tm_mday, tm.tm_mon + 1),
                    "date_field", "date_transform");
            }
        } else if (input.type() == typeid(std::chrono::system_clock::time_point)) {
            auto time_point = std::any_cast<std::chrono::system_clock::time_point>(input);
            auto time_t = std::chrono::system_clock::to_time_t(time_point);
            // Use thread-safe gmtime_r on Unix or gmtime_s on Windows
            #ifdef _WIN32
                gmtime_s(&tm, &time_t);
            #else
                gmtime_r(&time_t, &tm);
            #endif
        } else {
            throw common::TransformationException(
                std::format("Unsupported input type for date transformation: {}", input.type().name()),
                "date_field", "date_transform");
        }
        
        // Handle time addition if configured
        if (add_time_) {
            std::string time_to_add = default_time_.empty() ? "00:00:00" : default_time_;
            std::istringstream time_iss(time_to_add);
            int hour, minute, second;
            char colon1, colon2;
            time_iss >> hour >> colon1 >> minute >> colon2 >> second;
            if (!time_iss.fail() && colon1 == ':' && colon2 == ':') {
                tm.tm_hour = hour;
                tm.tm_min = minute;
                tm.tm_sec = second;
            }
        }
        
        // Format the output
        char buf[64];
        // Use thread-safe formatting
        {
            static std::mutex time_mutex;
            std::lock_guard<std::mutex> lock(time_mutex);
            std::strftime(buf, sizeof(buf), output_format_.c_str(), &tm);
        }
        logger->debug("[DateTransformation] Output string: {}", buf);
        return std::string(buf);
    } catch (const std::bad_any_cast& e) {
        logger->error("[DateTransformation] bad_any_cast: {}", e.what());
        throw;
    }
}

bool DateTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(std::string) ||
           input.type() == typeid(std::chrono::system_clock::time_point);
}

void DateTransformation::configure(const YAML::Node& params) {
    // Check if parameters are nested under a "parameters" key
    YAML::Node actual_params = params;
    if (params["parameters"]) {
        actual_params = params["parameters"];
    }
    
    if (actual_params["format"]) {
        input_format_ = actual_params["format"].as<std::string>();
    }
    if (actual_params["output_format"]) {
        output_format_ = actual_params["output_format"].as<std::string>();
    } else {
        // Auto-detect output format based on target field name
        if (params["target_column"]) {
            std::string target_column = params["target_column"].as<std::string>();
            if (target_column.find("datetime") != std::string::npos || 
                target_column.find("timestamp") != std::string::npos) {
                output_format_ = "%Y-%m-%d %H:%M:%S";
                add_time_ = true;
            }
        }
    }
    if (actual_params["timezone"]) {
        timezone_ = actual_params["timezone"].as<std::string>();
    }
    if (actual_params["add_time"]) {
        add_time_ = actual_params["add_time"].as<bool>();
    }
    if (actual_params["default_time"]) {
        default_time_ = actual_params["default_time"].as<std::string>();
    }
}

// Vocabulary Transformation Implementation
VocabularyTransformation::VocabularyTransformation(VocabularyService& vocabulary_service)
    : vocabulary_service_(vocabulary_service) {}

std::any VocabularyTransformation::transform(const std::any& input,
                                            core::ProcessingContext& context) {
    if (!validate_input(input)) {
        return default_concept_id_;
    }

    try {
        std::string value;

        if (input.type() == typeid(std::string)) {
            value = std::any_cast<std::string>(input);
        } else if (input.type() == typeid(const char*)) {
            value = std::any_cast<const char*>(input);
        } else {
            // Try to convert to string
            return default_concept_id_;
        }

        // Apply case sensitivity
        if (!case_sensitive_) {
            std::transform(value.begin(), value.end(), value.begin(), ::toupper);
        }

        // Check for circular references
        if (!allow_circular_ && !source_vocabulary_.empty() && 
            source_vocabulary_ == target_vocabulary_) {
            // Circular reference detected and not allowed, return original value
            return input;
        }

        // Look up concept ID
        int concept_id = vocabulary_service_.map_to_concept_id(
            value, vocabulary_name_);

        if (concept_id == 0) {
            context.log("warning", std::format("No mapping found for value '{}' in vocabulary '{}'",
                                             value, vocabulary_name_));
            return default_concept_id_;
        }

        return concept_id;

    } catch (const std::exception& e) {
        context.increment_errors();
        throw common::TransformationException(
            std::format("Vocabulary transformation failed: {}", e.what()),
            vocabulary_name_, "vocabulary_mapping");
    }
}

bool VocabularyTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    return input.type() == typeid(std::string) ||
           input.type() == typeid(const char*);
}

void VocabularyTransformation::configure(const YAML::Node& params) {
    if (!params["vocabulary"]) {
        throw common::TransformationException(
            "Vocabulary transformation requires 'vocabulary' parameter",
            "", "vocabulary_mapping");
    }

    vocabulary_name_ = params["vocabulary"].as<std::string>();

    if (params["source_vocabulary"]) {
        source_vocabulary_ = params["source_vocabulary"].as<std::string>();
    }
    if (params["target_vocabulary"]) {
        target_vocabulary_ = params["target_vocabulary"].as<std::string>();
    }
    if (params["default_value"]) {
        default_concept_id_ = params["default_value"].as<int>();
    }
    if (params["case_sensitive"]) {
        case_sensitive_ = params["case_sensitive"].as<bool>();
    }
    if (params["allow_circular"]) {
        allow_circular_ = params["allow_circular"].as<bool>();
    }
}

// Numeric Transformation Implementation
std::any NumericTransformation::transform(const std::any& input,
                                         core::ProcessingContext& context) {
    if (!validate_input(input)) {
        throw common::TransformationException("Invalid input for numeric transformation",
                                            "numeric_field", "numeric_transform");
    }

    try {
        double value = 0.0;

        // Convert input to double
        if (input.type() == typeid(double)) {
            value = std::any_cast<double>(input);
        } else if (input.type() == typeid(float)) {
            value = static_cast<double>(std::any_cast<float>(input));
        } else if (input.type() == typeid(int)) {
            value = static_cast<double>(std::any_cast<int>(input));
        } else if (input.type() == typeid(int64_t)) {
            value = static_cast<double>(std::any_cast<int64_t>(input));
        } else if (input.type() == typeid(std::string)) {
            value = std::stod(std::any_cast<std::string>(input));
        } else {
            throw common::TransformationException("Unsupported numeric type",
                                                "numeric_field", "numeric_transform");
        }

        // Validate input value for edge cases
        if (!std::isfinite(value)) {
            throw common::TransformationException("Invalid numeric value: infinity or NaN not supported",
                                                "numeric_field", "numeric_transform");
        }

        // Apply operation
        switch (operation_) {
            case Operation::Multiply:
                value *= operand_;
                break;
            case Operation::Divide:
                if (operand_ == 0) {
                    throw common::TransformationException("Division by zero",
                                                        "numeric_field", "numeric_transform");
                }
                value /= operand_;
                break;
            case Operation::Add:
                value += operand_;
                break;
            case Operation::Subtract:
                value -= operand_;
                break;
            case Operation::Round:
                value = std::round(value * std::pow(10, precision_)) / std::pow(10, precision_);
                break;
            case Operation::Floor:
                value = std::floor(value);
                break;
            case Operation::Ceiling:
                value = std::ceil(value);
                break;
            case Operation::Absolute:
                value = std::abs(value);
                break;
            default:
                break;
        }

        // Validate result after operation to detect overflow
        if (!std::isfinite(value)) {
            throw common::TransformationException("Numeric operation resulted in overflow or invalid value",
                                                "numeric_field", "numeric_transform");
        }

        // Apply constraints
        if (min_value_ && value < *min_value_) {
            value = *min_value_;
        }
        if (max_value_ && value > *max_value_) {
            value = *max_value_;
        }

        return value;

    } catch (const std::exception& e) {
        context.increment_errors();
        throw common::TransformationException(
            std::format("Numeric transformation failed: {}", e.what()),
            "numeric_field", "numeric_transform");
    }
}

bool NumericTransformation::validate_input(const std::any& input) const {
    if (!input.has_value()) return false;

    if (input.type() == typeid(double) ||
        input.type() == typeid(float) ||
        input.type() == typeid(int) ||
        input.type() == typeid(int64_t)) {
        return true;
    }

    if (input.type() == typeid(std::string)) {
        try {
            std::string str = std::any_cast<std::string>(input);
            std::stod(str); // Try to convert to double
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    return false;
}

void NumericTransformation::configure(const YAML::Node& params) {
    if (params["operation"]) {
        static const std::unordered_map<std::string, Operation> op_map = {
            {"multiply", Operation::Multiply},
            {"divide", Operation::Divide},
            {"add", Operation::Add},
            {"subtract", Operation::Subtract},
            {"round", Operation::Round},
            {"floor", Operation::Floor},
            {"ceiling", Operation::Ceiling},
            {"absolute", Operation::Absolute}
        };

        std::string op_str = params["operation"].as<std::string>();
        auto it = op_map.find(op_str);
        if (it != op_map.end()) {
            operation_ = it->second;
        }
    }

    if (params["operand"]) {
        operand_ = params["operand"].as<double>();
    }
    if (params["divisor"]) {
        operand_ = params["divisor"].as<double>();
    }
    if (params["precision"]) {
        precision_ = params["precision"].as<int>();
    }
    if (params["min_value"]) {
        min_value_ = params["min_value"].as<double>();
    }
    if (params["max_value"]) {
        max_value_ = params["max_value"].as<double>();
    }
    if (params["unit_conversion"]) {
        unit_conversion_ = params["unit_conversion"].as<std::string>();
    }
}

// String Concatenation Implementation
std::any StringConcatenationTransformation::transform(const std::any& input,
                                                     core::ProcessingContext& context) {
    if (!input.has_value()) {
        return std::string{};
    }

    // Handle Record input
    if (input.type() == typeid(core::Record)) {
        auto record = std::any_cast<core::Record>(input);
        
        // If source fields are not configured, use all fields from the record in sorted order
        std::vector<std::string> fields_to_use = source_fields_;
        if (fields_to_use.empty()) {
            auto field_names = record.getFieldNames();
            fields_to_use = std::vector<std::string>(field_names.begin(), field_names.end());
            std::sort(fields_to_use.begin(), fields_to_use.end());
        }
        
        // Build map of values
        std::unordered_map<std::string, std::any> values;
        for (const auto& field : fields_to_use) {
            values[field] = record.getField(field);
        }
        
        // Temporarily set source_fields_ for transform_multiple to use
        auto original_fields = source_fields_;
        source_fields_ = fields_to_use;
        auto result = transform_multiple(values, context);
        source_fields_ = original_fields;
        return result;
    }

    // Convert single value to string
    std::string result;
    if (input.type() == typeid(std::string)) {
        result = std::any_cast<std::string>(input);
    } else if (input.type() == typeid(const char*)) {
        result = std::any_cast<const char*>(input);
    } else if (input.type() == typeid(int)) {
        result = std::to_string(std::any_cast<int>(input));
    } else if (input.type() == typeid(int64_t)) {
        result = std::to_string(std::any_cast<int64_t>(input));
    } else if (input.type() == typeid(double)) {
        result = std::to_string(std::any_cast<double>(input));
    } else {
        result = "[unknown]";
    }

    return prefix_ + result + suffix_;
}

std::any StringConcatenationTransformation::transform_multiple(
    const std::unordered_map<std::string, std::any>& values,
    core::ProcessingContext& context) {

    std::ostringstream result;
    bool first = true;

    for (const auto& field : source_fields_) {
        auto it = values.find(field);
        if (it == values.end() || !it->second.has_value()) {
            if (!skip_empty_) {
                if (!first) result << separator_;
                first = false;
            }
            continue;
        }

        std::string value;

        // Convert to string
        if (it->second.type() == typeid(std::string)) {
            value = std::any_cast<std::string>(it->second);
        } else if (it->second.type() == typeid(const char*)) {
            value = std::any_cast<const char*>(it->second);
        } else if (it->second.type() == typeid(int)) {
            value = std::to_string(std::any_cast<int>(it->second));
        } else if (it->second.type() == typeid(int64_t)) {
            value = std::to_string(std::any_cast<int64_t>(it->second));
        } else if (it->second.type() == typeid(double)) {
            value = std::to_string(std::any_cast<double>(it->second));
        } else {
            value = "[unknown]";
        }

        // Trim whitespace if enabled
        if (trim_whitespace_) {
            // Remove leading whitespace
            value.erase(0, value.find_first_not_of(" \t\r\n"));
            // Remove trailing whitespace
            value.erase(value.find_last_not_of(" \t\r\n") + 1);
        }

        if (skip_empty_ && value.empty()) {
            continue;
        }

        if (!first) {
            result << separator_;
        }
        result << value;
        first = false;
    }

    return prefix_ + result.str() + suffix_;
}

bool StringConcatenationTransformation::validate_input(const std::any& input) const {
    return true; // String concatenation can handle any input
}

void StringConcatenationTransformation::configure(const YAML::Node& params) {
    if (params["separator"]) {
        separator_ = params["separator"].as<std::string>();
    }
    if (params["skip_empty"]) {
        skip_empty_ = params["skip_empty"].as<bool>();
    }
    if (params["trim_whitespace"]) {
        trim_whitespace_ = params["trim_whitespace"].as<bool>();
    }
    if (params["prefix"]) {
        prefix_ = params["prefix"].as<std::string>();
    }
    if (params["suffix"]) {
        suffix_ = params["suffix"].as<std::string>();
    }
    if (params["source_fields"]) {
        source_fields_ = params["source_fields"].as<std::vector<std::string>>();
    }
    // Support "fields" as alias for "source_fields" for backwards compatibility
    if (params["fields"]) {
        source_fields_ = params["fields"].as<std::vector<std::string>>();
    }
}

// Conditional Transformation Implementation
std::any ConditionalTransformation::transform(const std::any& input,
                                             core::ProcessingContext& context) {
    for (const auto& condition : conditions_) {
        if (evaluate_condition(condition, input)) {
            return condition.then_value;
        } else if (condition.else_value) {
            return *condition.else_value;
        }
        // If no else_value, continue to next condition or default
    }

    return default_value_;
}

bool ConditionalTransformation::evaluate_condition(const Condition& condition,
                                                  const std::any& value) const {
    if (!value.has_value()) {
        return condition.operator_type == "is_null";
    }

    if (condition.operator_type == "is_not_null") {
        return true;
    }

    // String comparisons
    if (value.type() == typeid(std::string) && condition.value.type() == typeid(std::string)) {
        std::string val = std::any_cast<std::string>(value);
        std::string cond_val = std::any_cast<std::string>(condition.value);

        if (condition.operator_type == "equals" || condition.operator_type == "==") {
            return val == cond_val;
        } else if (condition.operator_type == "not_equals" || condition.operator_type == "!=") {
            return val != cond_val;
        } else if (condition.operator_type == "contains") {
            return val.find(cond_val) != std::string::npos;
        } else if (condition.operator_type == "starts_with") {
            return val.starts_with(cond_val);
        } else if (condition.operator_type == "ends_with") {
            return val.ends_with(cond_val);
        } else if (condition.operator_type == "matches") {
            std::regex pattern(cond_val);
            return std::regex_match(val, pattern);
        }
    }

    // Numeric comparisons
    try {
        double val = 0.0;
        double cond_val = 0.0;

        // Convert value to double
        if (value.type() == typeid(double)) {
            val = std::any_cast<double>(value);
        } else if (value.type() == typeid(int)) {
            val = static_cast<double>(std::any_cast<int>(value));
        } else if (value.type() == typeid(int64_t)) {
            val = static_cast<double>(std::any_cast<int64_t>(value));
        }

        // Convert condition value to double
        if (condition.value.type() == typeid(double)) {
            cond_val = std::any_cast<double>(condition.value);
        } else if (condition.value.type() == typeid(int)) {
            cond_val = static_cast<double>(std::any_cast<int>(condition.value));
        }

        if (condition.operator_type == ">" || condition.operator_type == "greater_than") {
            return val > cond_val;
        } else if (condition.operator_type == ">=" || condition.operator_type == "greater_than_or_equal") {
            return val >= cond_val;
        } else if (condition.operator_type == "<" || condition.operator_type == "less_than") {
            return val < cond_val;
        } else if (condition.operator_type == "<=" || condition.operator_type == "less_than_or_equal") {
            return val <= cond_val;
        } else if (condition.operator_type == "==" || condition.operator_type == "equals") {
            return std::abs(val - cond_val) < 0.0001; // Floating point comparison
        } else if (condition.operator_type == "!=" || condition.operator_type == "not_equals") {
            return std::abs(val - cond_val) >= 0.0001;
        }
    } catch (...) {
        // Not numeric comparison
    }

    return false;
}

bool ConditionalTransformation::validate_input(const std::any& input) const {
    return true; // Conditional can handle any input
}

void ConditionalTransformation::configure(const YAML::Node& params) {
    if (params["conditions"]) {
        for (const auto& cond_node : params["conditions"]) {
            Condition condition;

            if (cond_node["field"]) {
                condition.field = cond_node["field"].as<std::string>();
            }

            if (cond_node["if"]) {
                std::string if_expr = cond_node["if"].as<std::string>();
                
                // Simple expression parser for common operators
                std::regex expr_regex(R"((\w+)\s*(==|!=|>=|<=|>|<|contains|starts_with|ends_with)\s*(['""]?)([^'"]*)\3)");
                std::smatch match;
                
                if (std::regex_search(if_expr, match, expr_regex)) {
                    condition.field = match[1].str();
                    condition.operator_type = match[2].str();
                    condition.value = match[4].str();
                } else {
                    throw common::ConfigurationException(
                        std::format("Invalid conditional expression: '{}'", if_expr));
                }
            }

            if (cond_node["operator"]) {
                condition.operator_type = cond_node["operator"].as<std::string>();
            }

            if (cond_node["value"]) {
                // Store as appropriate type based on the value
                if (cond_node["value"].IsScalar()) {
                    std::string str_val = cond_node["value"].as<std::string>();
                    // Try to parse as number
                    try {
                        if (str_val.find('.') != std::string::npos) {
                            condition.value = std::stod(str_val);
                        } else {
                            condition.value = std::stoi(str_val);
                        }
                    } catch (...) {
                        condition.value = str_val;
                    }
                } else {
                    condition.value = cond_node["value"].as<std::string>();
                }
            }

            if (cond_node["then"]) {
                condition.then_value = cond_node["then"].as<std::string>();
            }

            if (cond_node["else"]) {
                condition.else_value = cond_node["else"].as<std::string>();
            }

            conditions_.push_back(condition);
        }
    }

    if (params["default"]) {
        default_value_ = params["default"].as<std::string>();
    }
}

// Transformation Engine Implementation
TransformationEngine::TransformationEngine() {
    auto logger = common::Logger::get("omop-etl-transform");

    // Register default transformations in internal registry
    register_transformation("direct",
        []() { return std::make_unique<DirectTransformation>(); });

    register_transformation("date_transform",
        []() { return std::make_unique<DateTransformation>(); });

    register_transformation("date_calculation",
        []() { return std::make_unique<DateCalculationTransformation>(); });

    register_transformation("date_range_validation",
        []() { return std::make_unique<DateRangeValidationTransformation>(); });

    register_transformation("numeric_transform",
        []() { return std::make_unique<NumericTransformation>(); });

    register_transformation("string_concatenation",
        []() { return std::make_unique<StringConcatenationTransformation>(); });

    register_transformation("string_manipulation",
        []() { return std::make_unique<StringManipulationTransformation>(); });

    register_transformation("custom",
        []() { return std::make_unique<DirectTransformation>(); }); // Fallback for custom transformations

    register_transformation("conditional",
        []() { return std::make_unique<ConditionalTransformation>(); });

    register_transformation("advanced_numeric_transform",
        []() { return std::make_unique<AdvancedNumericTransformation>(); });

    register_transformation("numeric_validation",
        []() { return std::make_unique<NumericValidationTransformation>(); });

    register_transformation("advanced_conditional",
        []() { return std::make_unique<AdvancedConditionalTransformation>(); });
    register_transformation("lookup_table",
        []() { return std::make_unique<LookupTableTransformation>(); });
    
    register_transformation("string_transform",
        []() { return std::make_unique<UnifiedStringTransformation>(); });

    // Register anonymization transformations
    register_transformation("nhs_number_anonymizer",
        []() { return std::make_unique<NHSNumberAnonymizer>(); });

    register_transformation("date_to_age_anonymizer",
        []() { return std::make_unique<DateToAgeAnonymizer>(); });

    register_transformation("postcode_to_region_anonymizer",
        []() { return std::make_unique<PostcodeToRegionAnonymizer>(); });

    register_transformation("secure_hash_anonymizer",
        []() { return std::make_unique<SecureHashAnonymizer>(); });

    register_transformation("research_pseudonymizer",
        []() { return std::make_unique<ResearchPseudonymizer>(); });

    register_transformation("composite_anonymizer",
        []() { return std::make_unique<CompositeAnonymizer>(); });

    // Register in global registry as well
    auto& global_registry = TransformationRegistry::instance();

    global_registry.register_transformation("direct",
        []() { return std::make_unique<DirectTransformation>(); });

    global_registry.register_transformation("date_transform",
        []() { return std::make_unique<DateTransformation>(); });

    global_registry.register_transformation("date_calculation",
        []() { return std::make_unique<DateCalculationTransformation>(); });

    global_registry.register_transformation("date_range_validation",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<DateRangeValidationTransformation>(); 
        });

    global_registry.register_transformation("numeric_transform",
        []() { return std::make_unique<NumericTransformation>(); });

    global_registry.register_transformation("string_concatenation",
        []() { return std::make_unique<StringConcatenationTransformation>(); });

    global_registry.register_transformation("string_manipulation",
        []() { return std::make_unique<StringManipulationTransformation>(); });

    global_registry.register_transformation("custom",
        []() { return std::make_unique<DirectTransformation>(); }); // Fallback for custom transformations

    global_registry.register_transformation("conditional",
        []() { return std::make_unique<ConditionalTransformation>(); });

    global_registry.register_transformation("advanced_numeric_transform",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<AdvancedNumericTransformation>(); 
        });

    global_registry.register_transformation("numeric_validation",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<NumericValidationTransformation>(); 
        });

    global_registry.register_transformation("advanced_conditional",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<AdvancedConditionalTransformation>(); 
        });
    global_registry.register_transformation("lookup_table",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<LookupTableTransformation>(); 
        });
    
    global_registry.register_transformation("string_transform",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<UnifiedStringTransformation>(); 
        });

    // Register anonymization transformations in global registry
    global_registry.register_transformation("nhs_number_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<NHSNumberAnonymizer>(); 
        });

    global_registry.register_transformation("date_to_age_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<DateToAgeAnonymizer>(); 
        });

    global_registry.register_transformation("postcode_to_region_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<PostcodeToRegionAnonymizer>(); 
        });

    global_registry.register_transformation("secure_hash_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<SecureHashAnonymizer>(); 
        });

    global_registry.register_transformation("research_pseudonymizer",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<ResearchPseudonymizer>(); 
        });

    global_registry.register_transformation("composite_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> { 
            return std::make_unique<CompositeAnonymizer>(); 
        });

    // Set anonymization as mandatory by default for healthcare compliance
    anonymization_mandatory_ = true;

    // Note: vocabulary_mapping requires special handling due to dependency
}

void TransformationEngine::initialize(
    const std::unordered_map<std::string, std::any>& config,
    core::ProcessingContext& context) {

    auto logger = common::Logger::get("omop-etl-transform");
    logger->info("Initializing transformation engine");

    // Get table name from config
    // Check if test mode is enabled to bypass table mapping requirements
    bool test_mode = false;
    if (config.find("test_mode") != config.end()) {
        test_mode = std::any_cast<bool>(config.at("test_mode"));
    }

    if (config.find("table_name") != config.end()) {
        std::string table_name = std::any_cast<std::string>(config.at("table_name"));
        logger->info("About to get_table_mapping for table: {}", table_name);
        
        if (test_mode) {
            // In test mode, check if table mapping is provided
            logger->info("Test mode enabled - creating minimal table mapping");
            if (config.find("table_mapping") != config.end()) {
                // Use provided table mapping configuration
                YAML::Node table_mapping = std::any_cast<YAML::Node>(config.at("table_mapping"));
                current_mapping_ = common::TableMapping(table_mapping);
                logger->info("Using provided table mapping configuration");
            } else {
                // Create minimal table mapping if none provided
                YAML::Node mapping_node;
                mapping_node["source_table"] = table_name;
                mapping_node["target_table"] = table_name;
                current_mapping_ = common::TableMapping(mapping_node);
                logger->info("Created minimal table mapping");
            }
        } else {
            // Load table mapping
            auto& config_mgr = common::Config::instance();
            auto mapping = config_mgr.get_table_mapping(table_name);
            logger->info("get_table_mapping returned");
            if (!mapping) {
                throw common::ConfigurationException(
                    std::format("No mapping found for table '{}'", table_name));
            }
            current_mapping_ = *mapping;
        }
    } else {
        throw common::ConfigurationException("Missing required 'table_name' in config");
    }

    // Initialize vocabulary service if needed
    if (!VocabularyServiceManager::is_initialised()) {
        // This should be initialised by the pipeline
        logger->warn("Vocabulary service not initialised");
    } else {
        // Vocabulary service is available through VocabularyServiceManager
        try {
            // Register vocabulary transformation with service reference
            register_transformation("vocabulary_mapping",
                []() {
                    if (!VocabularyServiceManager::is_initialised()) {
                        throw common::ConfigurationException("Vocabulary service not available");
                    }
                    auto& vocab_service = VocabularyServiceManager::instance();
                    return std::make_unique<VocabularyTransformation>(vocab_service);
                });
        } catch (const std::exception& e) {
            logger->error("Failed to register vocabulary transformation: {}", e.what());
        }
    }

    // Initialize anonymization if mandatory
    if (anonymization_mandatory_) {
        logger->info("Anonymization is mandatory - initializing anonymization settings");
        
        // Create composite anonymizer for record-level anonymization
        record_anonymizer_ = std::make_unique<CompositeAnonymizer>();
        
        // Register anonymization transformations with the factory
        AnonymizationTransformationFactory::register_all_transformations();
        
        // Configure default anonymization settings from config if provided
        if (config.find("anonymization_config") != config.end()) {
            anonymization_config_ = std::any_cast<YAML::Node>(config.at("anonymization_config"));
            logger->info("Using provided anonymization configuration");
        } else {
            // Create default anonymization configuration for UK healthcare
            logger->info("Creating default UK healthcare anonymization configuration");
            anonymization_config_["enabled"] = true;
            anonymization_config_["compliance_level"] = "fully_anonymized";
            anonymization_config_["uk_healthcare_mode"] = true;
        }
        
        record_anonymizer_->configure(anonymization_config_);
        logger->info("Record anonymizer configured successfully");
    }

    logger->info("Transformation engine initialised for table: {} (anonymization: {})",
                current_mapping_.target_table(), 
                anonymization_mandatory_ ? "MANDATORY" : "disabled");
}

std::optional<core::Record> TransformationEngine::transform(
    const core::Record& record,
    core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-etl-transform");
    logger->debug("Starting transformation for record");
    
    // Increment processed count for every record that enters the transform method
    context.increment_processed();

    // Apply filters first, before any transformation logic
    if (current_mapping_.filters().IsDefined() && !current_mapping_.filters().IsNull()) {
        logger->debug("Applying filters");
        bool filter_passed = apply_filters(record, current_mapping_.filters());
        logger->debug("Filter result: {}", filter_passed ? "PASSED" : "FAILED");

        if (!filter_passed) {
            logger->debug("Record filtered out - returning std::nullopt immediately");
            records_filtered_++;
            context.increment_errors(); // Increment error count for filtered records
            return std::nullopt; // Early return - no transformation logic should execute
        }
        logger->debug("Record passed filters - proceeding to transformations");
    } else {
        logger->debug("No filters defined - proceeding to transformations");
    }

    // Check strict validation before attempting transformations
    auto strict_val_opt = context.get_data("strict_validation");
    bool strict_validation = false;
    if (strict_val_opt.has_value()) {
        try {
            strict_validation = std::any_cast<bool>(*strict_val_opt);
        } catch (const std::bad_any_cast&) {
            logger->warn("Invalid strict_validation value in context");
        }
    }

    if (strict_validation) {
        logger->debug("Strict validation enabled - checking for missing required fields");
        // For strict validation, check if any required fields are missing
        // This is a simplified check - in practice, this would be more comprehensive
        bool has_required_fields = true;
        for (const auto& rule : current_mapping_.transformations()) {
            try {
                record.getField(std::string(rule.source_column()));
            } catch (...) {
                // Any exception (missing field, etc) means validation fails
                logger->debug("Strict validation: missing required field '{}'", std::string(rule.source_column()));
                has_required_fields = false;
                break;
            }
        }

        if (!has_required_fields) {
            logger->debug("Record failed strict validation - missing required fields");
            validation_errors_++;
            context.increment_errors();
            return std::nullopt;
        }
    }

    try {
        // Apply transformations
        logger->debug("Applying transformations");
        auto transformed = apply_transformations(record, current_mapping_.transformations(), context);

        // Apply mandatory anonymization if enabled
        if (anonymization_mandatory_ && record_anonymizer_) {
            logger->debug("Applying mandatory anonymization to transformed record");
            try {
                // Apply anonymization to the entire record
                auto transformation_result = record_anonymizer_->transform_detailed(transformed, context);
                
                if (transformation_result.is_success()) {
                    logger->debug("Anonymization completed successfully");
                    
                    // Extract anonymization result metadata
                    bool contains_personal_data = false;
                    if (transformation_result.metadata.find("contains_personal_data") != transformation_result.metadata.end()) {
                        contains_personal_data = std::any_cast<bool>(transformation_result.metadata.at("contains_personal_data"));
                    }
                    
                    if (!contains_personal_data) {
                        logger->debug("No personal data remains after anonymization");
                        // Replace transformed record with anonymized version
                        if (transformation_result.value.type() == typeid(core::Record)) {
                            transformed = std::any_cast<core::Record>(transformation_result.value);
                        }
                        
                        // Add anonymization audit information to record metadata
                        auto metadata = transformed.getMetadata();
                        
                        if (transformation_result.metadata.find("method_used") != transformation_result.metadata.end()) {
                            metadata.custom["anonymization_method"] = std::any_cast<std::string>(transformation_result.metadata.at("method_used"));
                        }
                        metadata.custom["anonymization_level"] = "fully_anonymized";
                        metadata.custom["contains_personal_data"] = "false";
                        metadata.custom["anonymization_timestamp"] = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
                        transformed.setMetadata(metadata);
                        
                        // Log compliance status
                        std::string method_used = "composite_anonymizer";
                        if (transformation_result.metadata.find("method_used") != transformation_result.metadata.end()) {
                            method_used = std::any_cast<std::string>(transformation_result.metadata.at("method_used"));
                        }
                        
                        bool is_reversible = true;
                        if (transformation_result.metadata.find("is_reversible") != transformation_result.metadata.end()) {
                            is_reversible = std::any_cast<bool>(transformation_result.metadata.at("is_reversible"));
                        }
                        
                        int anonymization_level = 0;
                        if (transformation_result.metadata.find("anonymization_level") != transformation_result.metadata.end()) {
                            anonymization_level = std::any_cast<int>(transformation_result.metadata.at("anonymization_level"));
                        }
                        
                        context.log("info", std::format(
                            "Record anonymized successfully using {}: compliance={}, reversible={}", 
                            method_used,
                            anonymization_level == 0 ? "GDPR_COMPLIANT" : "NOT_COMPLIANT",
                            is_reversible ? "NO" : "YES")); // Note: reversible = NO is good for anonymization
                    } else {
                        logger->error("Anonymization failed - personal data still present in result");
                        context.increment_errors();
                        context.log("error", "Anonymization failed - personal data detected in anonymized result");
                        return std::nullopt;
                    }
                } else {
                    logger->error("Anonymization failed - transformation unsuccessful");
                    context.increment_errors();
                    context.log("error", std::format("Anonymization failed: {}", transformation_result.error_message.value_or("Unknown error")));
                    return std::nullopt;
                }
            } catch (const std::exception& e) {
                logger->error("Anonymization failed with exception: {}", e.what());
                context.increment_errors();
                context.log("error", std::format("Anonymization failed: {}", e.what()));
                return std::nullopt;
            }
        } else if (anonymization_mandatory_) {
            logger->error("Anonymization is mandatory but anonymizer not initialized");
            context.increment_errors();
            context.log("error", "Anonymization is mandatory but anonymizer not available");
            return std::nullopt;
        }

        // Validate if enabled
        bool validate_records = true; // Default value
        if (validate_records) {
            logger->debug("Applying validations");
            auto validation_result = apply_validations(transformed, current_mapping_.validations());
            if (!validation_result.is_valid()) {
                validation_errors_++;
                for (const auto& error : validation_result.errors()) {
                    context.log("warning",
                        std::format("Validation error for field '{}': {}",
                                  error.field_name, error.error_message));
                }
                logger->debug("Record failed validation, incrementing error count");
                context.increment_errors();
                return std::nullopt;
            }
        }

        records_transformed_++;
        logger->debug("Transformation completed successfully");
        return transformed;
    } catch (const common::TransformationException& e) {
        logger->error("[transform] Caught TransformationException: {}", e.what());
        transformation_errors_++;
        context.increment_errors();
        throw;
    } catch (const std::exception& e) {
        logger->error("std::exception caught: {}", e.what());
        context.increment_errors();
        throw;
    }
}

core::RecordBatch TransformationEngine::transform_batch(
    const core::RecordBatch& batch,
    core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-etl-transform");
    logger->debug("Starting batch transformation of {} records", batch.size());

    auto start_time = std::chrono::steady_clock::now();
    core::RecordBatch transformed_batch;
    transformed_batch.reserve(batch.size());

    for (const auto& record : batch) {
        try {
            auto result = transform(record, context);
            if (result) {
                transformed_batch.addRecord(std::move(*result));
                logger->debug("Record transformed successfully");
            } else {
                logger->debug("Record was filtered out");
            }
        } catch (const std::exception& e) {
            // Log error but continue with batch
            auto logger = common::Logger::get("omop-etl-transform");
            logger->error("[transform_batch] Caught exception: {}", e.what());
            context.log("error",
                std::format("Failed to transform record: {}", e.what()));
            context.increment_errors();
            records_filtered_++; // Count failed transformations as filtered records
        }
    }

    auto duration = std::chrono::steady_clock::now() - start_time;
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        total_transform_time_ += duration;
    }

    logger->debug("Batch transformation completed. Valid records: {}, Error count: {}",
                  transformed_batch.size(), context.error_count());
    return transformed_batch;
}

core::Record TransformationEngine::apply_transformations(
    const core::Record& record,
    const std::vector<common::TransformationRule>& rules,
    core::ProcessingContext& context) {
    auto logger = common::Logger::get("omop-etl-transform");
    logger->debug("[apply_transformations] Entered for record");

    core::Record transformed;
    
    // Preserve metadata from input record
    auto input_metadata = record.getMetadata();
    input_metadata.target_table = current_mapping_.target_table();
    transformed.setMetadata(input_metadata);

    for (size_t i = 0; i < rules.size(); ++i) {
        const auto& rule = rules[i];
        logger->debug("[apply_transformations] Rule {}: source_column={}, target_column={}, type={}", i, rule.source_column(), rule.target_column(), static_cast<int>(rule.type()));
        try {
            auto transformation = create_transformation(rule);

            if (rule.is_multi_column()) {
                // Multi-column transformation
                logger->debug("Applying multi-column transformation");
                std::unordered_map<std::string, std::any> values;
                for (const auto& col : rule.source_columns()) {
                    try {
                        values[col] = record.getField(col);
                        logger->debug("Got value for column: {} (type: {})", col, values[col].type().name());
                    } catch (const std::out_of_range&) {
                        // Field doesn't exist, skip
                        logger->debug("Field doesn't exist: {}", col);
                    } catch (const std::exception& e) {
                        // Re-throw other exceptions (including TransformationException)
                        logger->debug("Exception while getting field {}: {}", col, e.what());
                        throw;
                    }
                }

                if (auto* concat = dynamic_cast<StringConcatenationTransformation*>(
                        transformation.get())) {
                    logger->debug("Calling transform_multiple for StringConcatenationTransformation");
                    auto result = concat->transform_multiple(values, context);
                    logger->debug("Result for target column '{}' type: {}", rule.target_column(), result.type().name());
                    transformed.setField(std::string(rule.target_column()), result);
                    logger->debug("String concatenation completed");
                }
            } else {
                // Single column transformation
                logger->debug("Applying single-column transformation");
                try {
                    auto value = record.getField(std::string(rule.source_column()));
                    logger->debug("Source column '{}' value type: {}", rule.source_column(), value.type().name());
                    if (value.type() != typeid(void)) {
                        logger->debug("Got value for source column: {}", rule.source_column());
                        logger->debug("Calling transform for target column: {}", rule.target_column());
                        auto result = transformation->transform(value, context);
                        logger->debug("Result for target column '{}' type: {}", rule.target_column(), result.type().name());
                        transformed.setField(std::string(rule.target_column()), result);
                        logger->debug("Transformation completed for target column: {}", rule.target_column());

                        // Also store source value if different column
                        if (rule.source_column() != rule.target_column()) {
                            transformed.setField(std::string(rule.source_column()) + "_source", value);
                        }
                    } else {
                        logger->debug("No value for source column: {}", rule.source_column());
                    }
                } catch (const std::out_of_range&) {
                    // Field doesn't exist, skip
                    logger->debug("Field doesn't exist: {}", rule.source_column());
                } catch (const std::exception& e) {
                    // Re-throw other exceptions (including TransformationException)
                    logger->debug("Exception while getting field {}: {}", rule.source_column(), e.what());
                    throw;
                }
            }
        } catch (const common::TransformationException& e) {
            auto logger = common::Logger::get("omop-etl-transform");
            logger->error("[apply_transformations] Caught TransformationException: {}", e.what());
            logger->error("[apply_transformations] About to re-throw TransformationException");
            transformation_errors_++;
            throw; // Preserve the original exception type
        } catch (const std::exception& e) {
            auto logger = common::Logger::get("omop-etl-transform");
            logger->error("[apply_transformations] Caught std::exception: {}", e.what());
            throw common::TransformationException(
                std::format("Transformation failed for rule '{}': {}",
                          rule.target_column(), e.what()),
                rule.source_column(), rule.target_column());
        }
    }

    // Copy any unmapped fields
    for (const auto& field : record.getFieldNames()) {
        if (!transformed.hasField(field)) {
            try {
                auto value = record.getField(field);
                if (value.type() != typeid(void)) {
                    transformed.setField(field, value);
                    logger->debug("Copied unmapped field: {}", field);
                }
            } catch (const std::out_of_range&) {
                // Field doesn't exist, skip
                logger->debug("Field doesn't exist during copy: {}", field);
            } catch (const std::exception& e) {
                // Re-throw other exceptions (including TransformationException)
                logger->debug("Exception while getting field {}: {}", field, e.what());
                throw;
            }
        }
    }

    logger->debug("All transformations completed");
    return transformed;
}

std::unique_ptr<FieldTransformation> TransformationEngine::create_transformation(
    const common::TransformationRule& rule) {
    auto logger = common::Logger::get("omop-etl-transform");
    logger->debug("[create_transformation] Creating transformation for rule type: {}", static_cast<int>(rule.type()));

    std::string type_name;
    switch (rule.type()) {
        case common::TransformationRule::Type::Direct:
            type_name = "direct";
            break;
        case common::TransformationRule::Type::DateTransform:
            type_name = "date_transform";
            break;
        case common::TransformationRule::Type::VocabularyMapping:
            type_name = "vocabulary_mapping";
            break;
        case common::TransformationRule::Type::DateCalculation:
            type_name = "date_calculation";
            break;
        case common::TransformationRule::Type::NumericTransform:
            type_name = "numeric_transform";
            break;
        case common::TransformationRule::Type::StringConcatenation:
            type_name = "string_concatenation";
            break;
        case common::TransformationRule::Type::Conditional:
            type_name = "conditional";
            break;
        case common::TransformationRule::Type::Custom:
            // Check the original YAML parameters to determine the specific custom type
            if (rule.parameters()["validation_type"]) {
                // This is likely a string_transform (unified string transformation)
                type_name = "string_transform";
            } else if (rule.parameters()["operation"]) {
                std::string operation = rule.parameters()["operation"].as<std::string>();
                if (operation == "validate" || operation == "format") {
                    // This is also likely a string_transform
                    type_name = "string_transform";
                } else if (operation == "trim" || operation == "uppercase" || operation == "lowercase" || 
                    operation == "normalize_whitespace") {
                    type_name = "string_manipulation";
                } else {
                    type_name = "custom";
                }
            } else {
                type_name = "custom";
            }
            break;
    }

    auto it = transformation_factories_.find(type_name);
    if (it == transformation_factories_.end()) {
        // Try the global registry as fallback
        auto& global_registry = TransformationRegistry::instance();
        auto global_transformation = global_registry.create_transformation(type_name);
        if (global_transformation) {
            logger->debug("[create_transformation] Found transformation in global registry: '{}'", type_name);
            // Create a copy of parameters and add target column information
            YAML::Node config_params = YAML::Clone(rule.parameters());
            config_params["target_column"] = std::string(rule.target_column());
            config_params["source_column"] = std::string(rule.source_column());
            
            global_transformation->configure(config_params);
            logger->debug("[create_transformation] Transformation created successfully from global registry");
            return global_transformation;
        }
        
        logger->error("[create_transformation] Unknown transformation type: '{}'", type_name);
        throw common::TransformationException(
            std::format("Unknown transformation type: '{}'", type_name),
            "", type_name);
    }

    logger->debug("[create_transformation] Creating transformation instance for type: '{}'", type_name);
    auto transformation = it->second();
    logger->debug("[create_transformation] Configuring transformation with parameters");
    
    // Create a copy of parameters and add target column information
    YAML::Node config_params = YAML::Clone(rule.parameters());
    config_params["target_column"] = std::string(rule.target_column());
    config_params["source_column"] = std::string(rule.source_column());
    
    transformation->configure(config_params);
    logger->debug("[create_transformation] Transformation created successfully");

    return transformation;
}

bool TransformationEngine::apply_filters(const core::Record& record,
                                        const YAML::Node& filters) const {
    auto logger = common::Logger::get("omop-etl-transform");
    // Basic filter implementation: support 'not_null' operator
    if (!filters || !filters.IsSequence()) return true;
    for (const auto& filter : filters) {
        if (!filter["field"] || !filter["operator"]) continue;
        std::string field = filter["field"].as<std::string>();
        std::string op = filter["operator"].as<std::string>();
        if (op == "not_null") {
            try {
                auto value = record.getField(field);
                if (!value.has_value() || value.type() == typeid(void)) {
                    return false;
                }
            } catch (...) {
                // Any exception (missing field, etc) means filter fails
                logger->debug("[apply_filters] Exception or missing field '{}' - filtering out record", field);
                return false;
            }
        }
        // Add more operators as needed
    }
    return true;
}

void TransformationEngine::register_transformation(
    const std::string& type,
    std::function<std::unique_ptr<FieldTransformation>()> factory) {
    transformation_factories_[type] = std::move(factory);
}

omop::common::ValidationResult TransformationEngine::validate(const core::Record& record) const {
    auto logger = common::Logger::get("omop-etl-transform");
    logger->debug("Starting record validation");
    
    omop::common::ValidationResult result;

    try {
        // Check that required fields from transformation rules are present
        for (const auto& rule : current_mapping_.transformations()) {
            const std::string source_col = std::string(rule.source_column());
            
            // Check if field exists and is not null
            if (!record.hasField(source_col)) {
                omop::common::ValidationResult::ValidationError error;
                error.field_name = source_col;
                error.error_message = "Required field is missing";
                error.rule_name = "required_field_check";
                result.add_error(error);
                logger->debug("Validation error: Missing required field '{}'", source_col);
            } else {
                try {
                    auto field_value = record.getField(source_col);
                    
                    // Check for null/empty values based on field requirements
                    if (!field_value.has_value()) {
                        omop::common::ValidationResult::ValidationError error;
                        error.field_name = source_col;
                        error.error_message = "Required field has null value";
                        error.rule_name = "null_value_check";
                        result.add_error(error);
                        logger->debug("Validation error: Null value in required field '{}'", source_col);
                    }
                    
                    // Type-specific validation
                    if (field_value.type() == typeid(std::string)) {
                        std::string str_value = std::any_cast<std::string>(field_value);
                        if (str_value.empty()) {
                            omop::common::ValidationResult::ValidationError error;
                            error.field_name = source_col;
                            error.error_message = "Required string field is empty";
                            error.rule_name = "empty_string_check";
                            result.add_error(error);
                            logger->debug("Validation error: Empty string in required field '{}'", source_col);
                        }
                    }
                } catch (const std::exception& e) {
                    omop::common::ValidationResult::ValidationError error;
                    error.field_name = source_col;
                    error.error_message = std::format("Error accessing field: {}", e.what());
                    error.rule_name = "ACCESS_ERROR";
                    result.add_error(error);
                    logger->debug("Validation error: Cannot access field '{}': {}", source_col, e.what());
                }
            }
        }
        
        // Apply custom validations from configuration
        if (current_mapping_.validations().IsSequence()) {
            auto validation_result = apply_validations(record, current_mapping_.validations());
            result.merge(validation_result);
        }
        
        logger->debug("Record validation completed. Valid: {}, Errors: {}", 
                     result.is_valid(), result.errors().size());
                     
    } catch (const std::exception& e) {
        logger->error("Exception during record validation: {}", e.what());
        omop::common::ValidationResult::ValidationError error;
        error.field_name = "VALIDATION_SYSTEM";
        error.error_message = std::format("Validation system error: {}", e.what());
        error.rule_name = "SYSTEM_ERROR";
        result.add_error(error);
    }

    return result;
}

std::unordered_map<std::string, std::any> TransformationEngine::get_statistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    std::unordered_map<std::string, std::any> stats;
    stats["records_transformed"] = records_transformed_.load();
    stats["records_filtered"] = records_filtered_.load();
    stats["validation_errors"] = validation_errors_.load();
    stats["transformation_errors"] = transformation_errors_.load();
    stats["total_transform_time_seconds"] = total_transform_time_.count();

    return stats;
}

omop::common::ValidationResult TransformationEngine::apply_validations(
    const core::Record& record,
    const YAML::Node& validations) const {

    auto logger = common::Logger::get("omop-etl-transform");
    logger->debug("Applying custom validations from configuration");
    
    omop::common::ValidationResult result;

    try {
        if (!validations.IsSequence()) {
            logger->debug("No validations configured or invalid format");
            return result;
        }

        for (const auto& validation : validations) {
            if (!validation["field"] || !validation["type"]) {
                logger->warn("Invalid validation configuration: missing field or type");
                continue;
            }

            std::string field_name = validation["field"].as<std::string>();
            std::string validation_type = validation["type"].as<std::string>();
            
            logger->debug("Applying validation type '{}' to field '{}'", validation_type, field_name);

            // Check if field exists in record
            if (!record.hasField(field_name)) {
                if (validation_type == "required") {
                    omop::common::ValidationResult::ValidationError error;
                    error.field_name = field_name;
                    error.error_message = "Required field is missing from record";
                    error.rule_name = "MISSING_REQUIRED_FIELD";
                    result.add_error(error);
                    logger->debug("Validation failed: Required field '{}' is missing", field_name);
                }
                continue;
            }

            auto field_value = record.getField(field_name);

            // Apply specific validation types
            if (validation_type == "required") {
                if (!field_value.has_value()) {
                    omop::common::ValidationResult::ValidationError error;
                    error.field_name = field_name;
                    error.error_message = "Required field has null value";
                    error.rule_name = "NULL_REQUIRED_FIELD";
                    result.add_error(error);
                    logger->debug("Validation failed: Required field '{}' is null", field_name);
                }
            } else if (validation_type == "not_empty") {
                if (!field_value.has_value()) {
                    omop::common::ValidationResult::ValidationError error;
                    error.field_name = field_name;
                    error.error_message = "Field cannot be empty";
                    error.rule_name = "EMPTY_FIELD";
                    result.add_error(error);
                } else if (field_value.type() == typeid(std::string)) {
                    std::string str_value = std::any_cast<std::string>(field_value);
                    if (str_value.empty()) {
                        omop::common::ValidationResult::ValidationError error;
                        error.field_name = field_name;
                        error.error_message = "String field cannot be empty";
                        error.rule_name = "EMPTY_STRING_FIELD";
                        result.add_error(error);
                        logger->debug("Validation failed: String field '{}' is empty", field_name);
                    }
                }
            } else if (validation_type == "numeric") {
                if (field_value.has_value()) {
                    if (field_value.type() != typeid(int) && 
                        field_value.type() != typeid(double) && 
                        field_value.type() != typeid(float) &&
                        field_value.type() != typeid(long) &&
                        field_value.type() != typeid(short)) {
                        
                        // Try to parse string as number
                        if (field_value.type() == typeid(std::string)) {
                            std::string str_value = std::any_cast<std::string>(field_value);
                            try {
                                std::stod(str_value); // Test if parseable as double
                            } catch (const std::exception&) {
                                omop::common::ValidationResult::ValidationError error;
                                error.field_name = field_name;
                                error.error_message = "Field must be numeric";
                                error.rule_name = "NON_NUMERIC_FIELD";
                                result.add_error(error);
                                logger->debug("Validation failed: Field '{}' is not numeric", field_name);
                            }
                        } else {
                            omop::common::ValidationResult::ValidationError error;
                            error.field_name = field_name;
                            error.error_message = "Field must be numeric";
                            error.rule_name = "NON_NUMERIC_FIELD";
                            result.add_error(error);
                            logger->debug("Validation failed: Field '{}' is not numeric", field_name);
                        }
                    }
                }
            } else if (validation_type == "positive") {
                if (field_value.has_value()) {
                    double numeric_value = 0.0;
                    bool is_numeric = false;
                    
                    if (field_value.type() == typeid(int)) {
                        numeric_value = static_cast<double>(std::any_cast<int>(field_value));
                        is_numeric = true;
                    } else if (field_value.type() == typeid(double)) {
                        numeric_value = std::any_cast<double>(field_value);
                        is_numeric = true;
                    } else if (field_value.type() == typeid(float)) {
                        numeric_value = static_cast<double>(std::any_cast<float>(field_value));
                        is_numeric = true;
                    }
                    
                    if (is_numeric && numeric_value <= 0) {
                        omop::common::ValidationResult::ValidationError error;
                        error.field_name = field_name;
                        error.error_message = "Field must be positive";
                        error.rule_name = "NON_POSITIVE_FIELD";
                        result.add_error(error);
                        logger->debug("Validation failed: Field '{}' is not positive", field_name);
                    }
                }
            } else if (validation_type == "date_format") {
                if (field_value.has_value() && field_value.type() == typeid(std::string)) {
                    std::string date_str = std::any_cast<std::string>(field_value);
                    std::string format = validation["format"] ? 
                                       validation["format"].as<std::string>() : "%Y-%m-%d";
                    
                    std::tm tm = {};
                    std::istringstream iss(date_str);
                    iss >> std::get_time(&tm, format.c_str());
                    
                    if (iss.fail()) {
                        omop::common::ValidationResult::ValidationError error;
                        error.field_name = field_name;
                        error.error_message = std::format("Date format validation failed for format '{}'", format);
                        error.rule_name = "INVALID_DATE_FORMAT";
                        result.add_error(error);
                        logger->debug("Validation failed: Field '{}' has invalid date format", field_name);
                    }
                }
            }
            
            // Add range validation if specified
            if (validation["min"] || validation["max"]) {
                if (field_value.has_value()) {
                    double numeric_value = 0.0;
                    bool is_numeric = false;
                    
                    if (field_value.type() == typeid(int)) {
                        numeric_value = static_cast<double>(std::any_cast<int>(field_value));
                        is_numeric = true;
                    } else if (field_value.type() == typeid(double)) {
                        numeric_value = std::any_cast<double>(field_value);
                        is_numeric = true;
                    } else if (field_value.type() == typeid(float)) {
                        numeric_value = static_cast<double>(std::any_cast<float>(field_value));
                        is_numeric = true;
                    }
                    
                    if (is_numeric) {
                        if (validation["min"] && numeric_value < validation["min"].as<double>()) {
                            omop::common::ValidationResult::ValidationError error;
                            error.field_name = field_name;
                            error.error_message = std::format("Value {} is below minimum {}", 
                                                             numeric_value, validation["min"].as<double>());
                            error.rule_name = "VALUE_BELOW_MINIMUM";
                            result.add_error(error);
                        }
                        
                        if (validation["max"] && numeric_value > validation["max"].as<double>()) {
                            omop::common::ValidationResult::ValidationError error;
                            error.field_name = field_name;
                            error.error_message = std::format("Value {} is above maximum {}", 
                                                             numeric_value, validation["max"].as<double>());
                            error.rule_name = "VALUE_ABOVE_MAXIMUM";
                            result.add_error(error);
                        }
                    }
                }
            }
        }
        
        logger->debug("Custom validations completed. Valid: {}, Errors: {}", 
                     result.is_valid(), result.errors().size());
                     
    } catch (const std::exception& e) {
        logger->error("Exception during custom validation: {}", e.what());
        omop::common::ValidationResult::ValidationError error;
        error.field_name = "VALIDATION_SYSTEM";
        error.error_message = std::format("Custom validation error: {}", e.what());
        error.rule_name = "CUSTOM_VALIDATION_ERROR";
        result.add_error(error);
    }

    return result;
}

// TransformationEngineFactory Implementation
std::unordered_map<std::string,
    std::function<std::unique_ptr<TransformationEngine>()>> TransformationEngineFactory::engine_factories_;

std::unique_ptr<TransformationEngine> TransformationEngineFactory::create_for_table(
    const std::string& table_name,
    const common::ConfigurationManager& config) {

    auto it = engine_factories_.find(table_name);
    if (it != engine_factories_.end()) {
        return it->second();
    }

    // Default implementation - create a basic transformation engine
    auto engine = std::make_unique<TransformationEngine>();

    // Register default transformations
    engine->register_transformation("direct", []() { return std::make_unique<DirectTransformation>(); });
    engine->register_transformation("date_transform", []() { return std::make_unique<DateTransformation>(); });
    engine->register_transformation("vocabulary_mapping", []() {
        // For vocabulary mapping without database dependency, return direct transformation
        // A proper implementation would require vocabulary service initialization
        return std::make_unique<DirectTransformation>();
    });
    engine->register_transformation("numeric_transform", []() { return std::make_unique<NumericTransformation>(); });
    engine->register_transformation("string_concatenation", []() { return std::make_unique<StringConcatenationTransformation>(); });
    engine->register_transformation("conditional", []() { return std::make_unique<ConditionalTransformation>(); });

    return engine;
}

void TransformationEngineFactory::register_engine(const std::string& table_name,
    std::function<std::unique_ptr<TransformationEngine>()> factory) {
    engine_factories_[table_name] = std::move(factory);
}

} // namespace omop::transform