#include "anonymization_transformations.h"
#include "common/utilities.h"
#include <nlohmann/json.hpp>
#include <algorithm>
#include <ctime>
#include <iomanip>
#include <sstream>

namespace omop::transform {

// AnonymizationTransformation base class implementation

AnonymizationTransformation::AnonymizationTransformation(const std::string& method_name)
    : method_name_(method_name) {
}

TransformationResult AnonymizationTransformation::transform_detailed(
    const std::any& input, core::ProcessingContext& context) {
    
    try {
        if (!validate_input(input)) {
            return TransformationResult{
                std::any{}, false, {},
                "Invalid input for anonymization transformation",
                {}
            };
        }

        auto anonymization_result = apply_anonymization(input, context);
        
        // Create transformation result
        TransformationResult result;
        result.value = anonymization_result.anonymized_value;
        result.success = true;
        result.metadata["anonymization_level"] = static_cast<int>(anonymization_result.compliance_level);
        result.metadata["contains_personal_data"] = anonymization_result.contains_personal_data;
        result.metadata["is_reversible"] = anonymization_result.is_reversible;
        result.metadata["method_used"] = anonymization_result.method_used;
        
        if (anonymization_result.audit_log) {
            result.metadata["audit_log"] = *anonymization_result.audit_log;
        }

        // Add anonymization metadata
        for (const auto& [key, value] : anonymization_result.metadata) {
            result.metadata["anon_" + key] = value;
        }

        return result;
        
    } catch (const std::exception& e) {
        return TransformationResult{
            std::any{}, false, {},
            "Anonymization transformation failed: " + std::string(e.what()),
            {}
        };
    }
}

std::string AnonymizationTransformation::create_audit_log(
    const std::string& input_type, bool success) const {
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    nlohmann::json audit_entry;
    audit_entry["timestamp"] = time_t;
    audit_entry["method"] = method_name_;
    audit_entry["input_type"] = input_type;
    audit_entry["success"] = success;
    audit_entry["compliance_level"] = static_cast<int>(get_compliance_level());
    audit_entry["contains_personal_data"] = contains_personal_data();
    audit_entry["is_reversible"] = is_reversible();
    
    return audit_entry.dump();
}

std::string AnonymizationTransformation::generate_secure_salt(size_t length) const {
    std::vector<unsigned char> salt(length);
    if (RAND_bytes(salt.data(), static_cast<int>(length)) != 1) {
        throw common::TransformationException(
            "Failed to generate secure random salt", get_type(), "generate_secure_salt");
    }
    
    // Convert to base64
    std::string result;
    result.reserve(((length + 2) / 3) * 4);
    
    const char base64_chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    
    for (size_t i = 0; i < length; i += 3) {
        unsigned char b0 = salt[i];
        unsigned char b1 = (i + 1 < length) ? salt[i + 1] : 0;
        unsigned char b2 = (i + 2 < length) ? salt[i + 2] : 0;
        
        result += base64_chars[b0 >> 2];
        result += base64_chars[((b0 & 0x03) << 4) | (b1 >> 4)];
        result += (i + 1 < length) ? base64_chars[((b1 & 0x0f) << 2) | (b2 >> 6)] : '=';
        result += (i + 2 < length) ? base64_chars[b2 & 0x3f] : '=';
    }
    
    return result;
}

// NHSNumberAnonymizer implementation

NHSNumberAnonymizer::NHSNumberAnonymizer() 
    : AnonymizationTransformation("NHS_Number_Anonymizer"),
      hash_iterations_(100000),
      use_time_based_salt_(false) {
    
    // Generate secure salts
    global_salt_ = generate_secure_salt(32);
    study_specific_salt_ = generate_secure_salt(16);
}

bool NHSNumberAnonymizer::validate_input(const std::any& input) const {
    try {
        if (input.type() == typeid(std::string)) {
            const auto& nhs_number = std::any_cast<const std::string&>(input);
            return is_valid_nhs_number(nhs_number);
        }
        return false;
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

void NHSNumberAnonymizer::configure(const YAML::Node& params) {
    config_ = params;
    
    if (params["global_salt"]) {
        global_salt_ = params["global_salt"].as<std::string>();
    }
    
    if (params["study_specific_salt"]) {
        study_specific_salt_ = params["study_specific_salt"].as<std::string>();
    }
    
    if (params["hash_iterations"]) {
        hash_iterations_ = params["hash_iterations"].as<size_t>();
        // Ensure minimum security
        if (hash_iterations_ < 10000) {
            hash_iterations_ = 10000;
        }
    }
    
    if (params["use_time_based_salt"]) {
        use_time_based_salt_ = params["use_time_based_salt"].as<bool>();
    }
}

AnonymizationResult NHSNumberAnonymizer::apply_anonymization(
    const std::any& input, core::ProcessingContext& context) {
    
    const auto& nhs_number = std::any_cast<const std::string&>(input);
    
    AnonymizationResult result;
    result.method_used = "secure_hash_pbkdf2";
    result.compliance_level = AnonymizationLevel::FULLY_ANONYMIZED;
    result.contains_personal_data = false;
    result.is_reversible = false;
    
    try {
        auto anonymized_hash = generate_secure_hash(nhs_number);
        result.anonymized_value = anonymized_hash;
        result.metadata["original_length"] = nhs_number.length();
        result.metadata["hash_algorithm"] = "PBKDF2-SHA256";
        result.metadata["iterations"] = static_cast<int>(hash_iterations_);
        result.audit_log = create_audit_log("NHS_NUMBER", true);
        
    } catch (const std::exception& e) {
        result.anonymized_value = std::string("ANONYMIZATION_FAILED");
        result.metadata["error"] = e.what();
        result.audit_log = create_audit_log("NHS_NUMBER", false);
    }
    
    return result;
}

bool NHSNumberAnonymizer::is_valid_nhs_number(const std::string& nhs_number) const {
    // Remove spaces and check length
    std::string cleaned_number = nhs_number;
    cleaned_number.erase(std::remove(cleaned_number.begin(), cleaned_number.end(), ' '), 
                        cleaned_number.end());
    
    if (cleaned_number.length() != 10) {
        return false;
    }
    
    // Check all characters are digits
    if (!std::all_of(cleaned_number.begin(), cleaned_number.end(), ::isdigit)) {
        return false;
    }
    
    // Validate NHS number check digit using Modulus 11 algorithm
    int sum = 0;
    for (int i = 0; i < 9; ++i) {
        sum += (cleaned_number[i] - '0') * (10 - i);
    }
    
    int check_digit = 11 - (sum % 11);
    if (check_digit == 11) check_digit = 0;
    if (check_digit == 10) return false; // Invalid NHS number
    
    return (check_digit == (cleaned_number[9] - '0'));
}

std::string NHSNumberAnonymizer::generate_secure_hash(const std::string& nhs_number) const {
    std::string salt = global_salt_ + study_specific_salt_;
    
    if (use_time_based_salt_) {
        // Add time-based component (rotates every 90 days)
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto time_salt = std::to_string(time_t / (90 * 24 * 60 * 60)); // 90 days
        salt += time_salt;
    }
    
    // Use PBKDF2 with SHA-256
    unsigned char hash[32];
    if (PKCS5_PBKDF2_HMAC(nhs_number.c_str(), static_cast<int>(nhs_number.length()),
                         reinterpret_cast<const unsigned char*>(salt.c_str()),
                         static_cast<int>(salt.length()),
                         static_cast<int>(hash_iterations_),
                         EVP_sha256(), 32, hash) != 1) {
        throw common::TransformationException(
            "PBKDF2 hash generation failed", get_type(), "generate_secure_hash");
    }
    
    // Convert to hex string
    std::ostringstream hex_stream;
    hex_stream << std::hex << std::setfill('0');
    for (int i = 0; i < 32; ++i) {
        hex_stream << std::setw(2) << static_cast<unsigned>(hash[i]);
    }
    
    return hex_stream.str();
}

// DateToAgeAnonymizer implementation

DateToAgeAnonymizer::DateToAgeAnonymizer() 
    : AnonymizationTransformation("Date_To_Age_Anonymizer"),
      use_current_date_as_reference_(true),
      minimum_age_years_(0),
      maximum_age_years_(150) {
    
    // UK date formats
    supported_formats_ = {
        "%d/%m/%Y",           // 15/03/1985
        "%d-%m-%Y",           // 15-03-1985
        "%Y-%m-%d",           // 1985-03-15 (ISO)
        "%d/%m/%Y %H:%M:%S",  // 15/03/1985 14:30:00
        "%d-%m-%Y %H:%M:%S",  // 15-03-1985 14:30:00
        "%Y-%m-%d %H:%M:%S"   // 1985-03-15 14:30:00
    };
    
    reference_date_ = std::chrono::system_clock::now();
}

bool DateToAgeAnonymizer::validate_input(const std::any& input) const {
    try {
        if (input.type() == typeid(std::string)) {
            const auto& date_str = std::any_cast<const std::string&>(input);
            try {
                parse_uk_date(date_str);
                return true;
            } catch (...) {
                return false;
            }
        } else if (input.type() == typeid(std::chrono::system_clock::time_point)) {
            return true;
        }
        return false;
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

void DateToAgeAnonymizer::configure(const YAML::Node& params) {
    config_ = params;
    
    if (params["reference_date"]) {
        auto ref_date_str = params["reference_date"].as<std::string>();
        reference_date_ = parse_uk_date(ref_date_str);
        use_current_date_as_reference_ = false;
    }
    
    if (params["minimum_age_years"]) {
        minimum_age_years_ = params["minimum_age_years"].as<int>();
    }
    
    if (params["maximum_age_years"]) {
        maximum_age_years_ = params["maximum_age_years"].as<int>();
    }
    
    if (params["supported_formats"]) {
        supported_formats_ = params["supported_formats"].as<std::vector<std::string>>();
    }
}

AnonymizationResult DateToAgeAnonymizer::apply_anonymization(
    const std::any& input, core::ProcessingContext& context) {
    
    AnonymizationResult result;
    result.method_used = "date_to_age_conversion";
    result.compliance_level = AnonymizationLevel::FULLY_ANONYMIZED;
    result.contains_personal_data = false;
    result.is_reversible = false;
    
    try {
        std::chrono::system_clock::time_point birth_date;
        
        if (input.type() == typeid(std::string)) {
            const auto& date_str = std::any_cast<const std::string&>(input);
            birth_date = parse_uk_date(date_str);
            result.metadata["original_format"] = "string";
            result.metadata["original_value"] = date_str;
        } else {
            birth_date = std::any_cast<std::chrono::system_clock::time_point>(input);
            result.metadata["original_format"] = "time_point";
        }
        
        auto ref_date = use_current_date_as_reference_ ? 
                       std::chrono::system_clock::now() : reference_date_;
        
        auto age = calculate_age(birth_date, ref_date);
        
        // Validate age is reasonable
        if (age.years < minimum_age_years_ || age.years > maximum_age_years_) {
            result.anonymized_value = nlohmann::json{{"years", -1}, {"months", -1}};
            result.metadata["error"] = "Age outside valid range";
        } else {
            result.anonymized_value = age.to_json();
            result.metadata["calculated_age_years"] = age.years;
            result.metadata["calculated_age_months"] = age.months;
        }
        
        result.audit_log = create_audit_log("DATE", true);
        
    } catch (const std::exception& e) {
        result.anonymized_value = nlohmann::json{{"years", -1}, {"months", -1}};
        result.metadata["error"] = e.what();
        result.audit_log = create_audit_log("DATE", false);
    }
    
    return result;
}

DateToAgeAnonymizer::AgeYearsMonths DateToAgeAnonymizer::calculate_age(
    const std::chrono::system_clock::time_point& birth_date,
    const std::chrono::system_clock::time_point& reference_date) const {
    
    auto birth_time_t = std::chrono::system_clock::to_time_t(birth_date);
    auto ref_time_t = std::chrono::system_clock::to_time_t(reference_date);
    
    std::tm* birth_tm = std::localtime(&birth_time_t);
    std::tm* ref_tm = std::localtime(&ref_time_t);
    
    AgeYearsMonths age;
    age.years = ref_tm->tm_year - birth_tm->tm_year;
    age.months = ref_tm->tm_mon - birth_tm->tm_mon;
    
    // Adjust for month/day differences
    if (age.months < 0) {
        age.years--;
        age.months += 12;
    }
    
    // Fine-tune based on day of month
    if (ref_tm->tm_mday < birth_tm->tm_mday) {
        if (age.months > 0) {
            age.months--;
        } else {
            age.years--;
            age.months = 11;
        }
    }
    
    // Ensure non-negative results
    if (age.years < 0) {
        age.years = 0;
        age.months = 0;
    }
    
    return age;
}

std::chrono::system_clock::time_point DateToAgeAnonymizer::parse_uk_date(
    const std::string& date_str) const {
    
    for (const auto& format : supported_formats_) {
        std::tm tm = {};
        std::istringstream ss(date_str);
        ss >> std::get_time(&tm, format.c_str());
        
        if (!ss.fail()) {
            auto time_t = std::mktime(&tm);
            if (time_t != -1) {
                return std::chrono::system_clock::from_time_t(time_t);
            }
        }
    }
    
    throw common::TransformationException(
        "Unable to parse date string: " + date_str, get_type(), "parse_uk_date");
}

// PostcodeToRegionAnonymizer implementation

PostcodeToRegionAnonymizer::PostcodeToRegionAnonymizer() 
    : AnonymizationTransformation("Postcode_To_Region_Anonymizer"),
      minimum_population_size_(10000),
      use_nhs_regions_(true),
      uk_postcode_regex_(R"(^[A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2}$)") {
    
    initialize_region_mapping();
}

bool PostcodeToRegionAnonymizer::validate_input(const std::any& input) const {
    try {
        if (input.type() == typeid(std::string)) {
            const auto& postcode = std::any_cast<const std::string&>(input);
            return is_valid_uk_postcode(postcode);
        }
        return false;
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

void PostcodeToRegionAnonymizer::configure(const YAML::Node& params) {
    config_ = params;
    
    if (params["minimum_population_size"]) {
        minimum_population_size_ = params["minimum_population_size"].as<size_t>();
    }
    
    if (params["use_nhs_regions"]) {
        use_nhs_regions_ = params["use_nhs_regions"].as<bool>();
    }
    
    // Re-initialize mapping if configuration changed
    initialize_region_mapping();
}

AnonymizationResult PostcodeToRegionAnonymizer::apply_anonymization(
    const std::any& input, core::ProcessingContext& context) {
    
    std::string postcode;
    try {
        postcode = std::any_cast<std::string>(input);
    } catch (const std::bad_any_cast& e) {
        // Try to cast as const string reference
        try {
            postcode = std::any_cast<const std::string&>(input);
        } catch (const std::bad_any_cast& e2) {
            throw common::TransformationException(
                "Invalid input type for postcode anonymization - expected string",
                get_type(), "apply_anonymization");
        }
    }
    
    AnonymizationResult result;
    result.method_used = "postcode_to_region_aggregation";
    result.compliance_level = AnonymizationLevel::FULLY_ANONYMIZED;
    result.contains_personal_data = false;
    result.is_reversible = false;
    
    try {
        auto region = map_postcode_to_region(postcode);
        
        nlohmann::json region_data;
        region_data["region_code"] = region.region_code;
        region_data["region_name"] = region.region_name;
        if (use_nhs_regions_) {
            region_data["nhs_region"] = region.nhs_region;
        }
        
        result.anonymized_value = region_data;
        result.metadata["original_postcode_area"] = postcode.substr(0, 2);
        result.metadata["population_size"] = static_cast<int>(region.population_size);
        result.metadata["k_anonymity"] = region.population_size >= minimum_population_size_;
        result.audit_log = create_audit_log("POSTCODE", true);
        
    } catch (const std::exception& e) {
        nlohmann::json unknown_region;
        unknown_region["region_code"] = "UNKNOWN";
        unknown_region["region_name"] = "Unknown Region";
        unknown_region["nhs_region"] = "Unknown";
        
        result.anonymized_value = unknown_region;
        result.metadata["error"] = e.what();
        result.audit_log = create_audit_log("POSTCODE", false);
    }
    
    return result;
}

void PostcodeToRegionAnonymizer::initialize_region_mapping() {
    // UK postcode area to region mapping (representative subset)
    // In production, this would be loaded from a comprehensive database
    
    region_mapping_["SW"] = {"R001", "South West London", "NHS London", 50000};
    region_mapping_["M"] = {"R002", "Greater Manchester", "NHS North West", 280000};
    region_mapping_["B"] = {"R003", "Birmingham", "NHS Midlands", 120000};
    region_mapping_["E"] = {"R004", "East London", "NHS London", 90000};
    region_mapping_["WC"] = {"R005", "Central London", "NHS London", 40000};
    region_mapping_["SE"] = {"R006", "South East London", "NHS London", 75000};
    region_mapping_["N"] = {"R007", "North London", "NHS London", 85000};
    region_mapping_["NW"] = {"R008", "North West London", "NHS London", 70000};
    region_mapping_["W"] = {"R009", "West London", "NHS London", 60000};
    region_mapping_["EC"] = {"R010", "East Central London", "NHS London", 30000};
    region_mapping_["WD"] = {"R011", "Hertfordshire", "NHS East of England", 25000};
    region_mapping_["AL"] = {"R012", "Hertfordshire", "NHS East of England", 20000};
    region_mapping_["HP"] = {"R013", "Buckinghamshire", "NHS South East", 35000};
    region_mapping_["SL"] = {"R014", "Berkshire", "NHS South East", 40000};
    region_mapping_["RG"] = {"R015", "Berkshire", "NHS South East", 45000};
    region_mapping_["GU"] = {"R016", "Surrey", "NHS South East", 55000};
    region_mapping_["KT"] = {"R017", "Surrey", "NHS South East", 30000};
    region_mapping_["CR"] = {"R018", "Surrey", "NHS South East", 38000};
    region_mapping_["SM"] = {"R019", "Surrey", "NHS South East", 32000};
    region_mapping_["TW"] = {"R020", "Middlesex", "NHS London", 48000};
    
    // Add more comprehensive mapping for production use
}

PostcodeToRegionAnonymizer::UKRegion PostcodeToRegionAnonymizer::map_postcode_to_region(
    const std::string& postcode) const {
    
    // Extract postcode area (first 1-2 letters)
    std::string area;
    for (char c : postcode) {
        if (std::isalpha(c)) {
            area += c;
        } else {
            break;
        }
    }
    
    auto it = region_mapping_.find(area);
    if (it != region_mapping_.end() && it->second.population_size >= minimum_population_size_) {
        return it->second;
    }
    
    // Return generic region for unmapped or low-population postcodes
    return {"R999", "Other UK Region", "NHS Other", minimum_population_size_};
}

bool PostcodeToRegionAnonymizer::is_valid_uk_postcode(const std::string& postcode) const {
    std::string upper_postcode = postcode;
    std::transform(upper_postcode.begin(), upper_postcode.end(), 
                  upper_postcode.begin(), ::toupper);
    
    return std::regex_match(upper_postcode, uk_postcode_regex_);
}

// SecureHashAnonymizer implementation

SecureHashAnonymizer::SecureHashAnonymizer() 
    : AnonymizationTransformation("Secure_Hash_Anonymizer"),
      algorithm_("SHA256"),
      iterations_(100000),
      output_length_(32),
      include_timestamp_salt_(false) {
    
    global_salt_ = generate_secure_salt(32);
    field_specific_salt_ = generate_secure_salt(16);
}

bool SecureHashAnonymizer::validate_input(const std::any& input) const {
    // Can hash any type that can be converted to string
    return true;
}

void SecureHashAnonymizer::configure(const YAML::Node& params) {
    config_ = params;
    
    if (params["algorithm"]) {
        algorithm_ = params["algorithm"].as<std::string>();
    }
    
    if (params["global_salt"]) {
        global_salt_ = params["global_salt"].as<std::string>();
    }
    
    if (params["field_specific_salt"]) {
        field_specific_salt_ = params["field_specific_salt"].as<std::string>();
    }
    
    if (params["iterations"]) {
        iterations_ = params["iterations"].as<size_t>();
        if (iterations_ < 10000) iterations_ = 10000; // Minimum security
    }
    
    if (params["output_length"]) {
        output_length_ = params["output_length"].as<size_t>();
    }
    
    if (params["include_timestamp_salt"]) {
        include_timestamp_salt_ = params["include_timestamp_salt"].as<bool>();
    }
}

AnonymizationResult SecureHashAnonymizer::apply_anonymization(
    const std::any& input, core::ProcessingContext& context) {
    
    AnonymizationResult result;
    result.method_used = "secure_hash_" + algorithm_;
    result.compliance_level = AnonymizationLevel::FULLY_ANONYMIZED;
    result.contains_personal_data = false;
    result.is_reversible = false;
    
    try {
        auto input_string = convert_to_string(input);
        auto hash = generate_pbkdf2_hash(input_string);
        
        result.anonymized_value = hash;
        result.metadata["algorithm"] = algorithm_;
        result.metadata["iterations"] = static_cast<int>(iterations_);
        result.metadata["output_length"] = static_cast<int>(output_length_);
        result.metadata["original_type"] = input.type().name();
        result.audit_log = create_audit_log("GENERIC", true);
        
    } catch (const std::exception& e) {
        result.anonymized_value = std::string("HASH_FAILED");
        result.metadata["error"] = e.what();
        result.audit_log = create_audit_log("GENERIC", false);
    }
    
    return result;
}

std::string SecureHashAnonymizer::convert_to_string(const std::any& input) const {
    if (input.type() == typeid(std::string)) {
        return std::any_cast<std::string>(input);
    } else if (input.type() == typeid(int)) {
        return std::to_string(std::any_cast<int>(input));
    } else if (input.type() == typeid(double)) {
        return std::to_string(std::any_cast<double>(input));
    } else if (input.type() == typeid(bool)) {
        return std::any_cast<bool>(input) ? "true" : "false";
    } else if (input.type() == typeid(std::chrono::system_clock::time_point)) {
        auto time_point = std::any_cast<std::chrono::system_clock::time_point>(input);
        auto time_t = std::chrono::system_clock::to_time_t(time_point);
        return std::to_string(time_t);
    }
    
    // Generic fallback - not perfect but handles most types
    return "unknown_type";
}

std::string SecureHashAnonymizer::generate_pbkdf2_hash(const std::string& input) const {
    std::string salt = global_salt_ + field_specific_salt_;
    
    if (include_timestamp_salt_) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        salt += std::to_string(time_t / (24 * 60 * 60)); // Daily rotation
    }
    
    std::vector<unsigned char> hash(output_length_);
    
    const EVP_MD* md = nullptr;
    if (algorithm_ == "SHA256") {
        md = EVP_sha256();
    } else if (algorithm_ == "SHA512") {
        md = EVP_sha512();
    } else {
        md = EVP_sha256(); // Default fallback
    }
    
    if (PKCS5_PBKDF2_HMAC(input.c_str(), static_cast<int>(input.length()),
                         reinterpret_cast<const unsigned char*>(salt.c_str()),
                         static_cast<int>(salt.length()),
                         static_cast<int>(iterations_),
                         md, static_cast<int>(output_length_), hash.data()) != 1) {
        throw common::TransformationException(
            "PBKDF2 hash generation failed", get_type(), "generate_pbkdf2_hash");
    }
    
    // Convert to hex string
    std::ostringstream hex_stream;
    hex_stream << std::hex << std::setfill('0');
    for (size_t i = 0; i < output_length_; ++i) {
        hex_stream << std::setw(2) << static_cast<unsigned>(hash[i]);
    }
    
    return hex_stream.str();
}

// ResearchPseudonymizer implementation

ResearchPseudonymizer::ResearchPseudonymizer() 
    : AnonymizationTransformation("Research_Pseudonymizer"),
      pseudonym_length_(16),
      time_limited_(true),
      pseudonym_expiry_(std::chrono::hours(24 * 90)) { // 90 days
    
    research_key_ = generate_secure_salt(32);
    pseudonym_format_ = "RES_{:08X}";
}

bool ResearchPseudonymizer::validate_input(const std::any& input) const {
    try {
        if (input.type() == typeid(std::string)) {
            return !std::any_cast<const std::string&>(input).empty();
        }
        return false;
    } catch (const std::bad_any_cast&) {
        return false;
    }
}

void ResearchPseudonymizer::configure(const YAML::Node& params) {
    config_ = params;
    
    if (params["study_id"]) {
        study_id_ = params["study_id"].as<std::string>();
    }
    
    if (params["research_key"]) {
        research_key_ = params["research_key"].as<std::string>();
    }
    
    if (params["pseudonym_format"]) {
        pseudonym_format_ = params["pseudonym_format"].as<std::string>();
    }
    
    if (params["pseudonym_length"]) {
        pseudonym_length_ = params["pseudonym_length"].as<size_t>();
    }
    
    if (params["time_limited"]) {
        time_limited_ = params["time_limited"].as<bool>();
    }
    
    if (params["pseudonym_expiry_days"]) {
        auto days = params["pseudonym_expiry_days"].as<int>();
        pseudonym_expiry_ = std::chrono::hours(24 * days);
    }
}

AnonymizationResult ResearchPseudonymizer::apply_anonymization(
    const std::any& input, core::ProcessingContext& context) {
    
    std::string identifier;
    try {
        identifier = std::any_cast<std::string>(input);
    } catch (const std::bad_any_cast& e) {
        // Try to cast as const string reference
        try {
            identifier = std::any_cast<const std::string&>(input);
        } catch (const std::bad_any_cast& e2) {
            throw common::TransformationException(
                "Invalid input type for research pseudonymization - expected string",
                get_type(), "apply_anonymization");
        }
    }
    
    AnonymizationResult result;
    result.method_used = "research_pseudonymization";
    result.compliance_level = AnonymizationLevel::PSEUDONYMIZED;
    result.contains_personal_data = false;
    result.is_reversible = false; // Cannot be reversed without key
    
    try {
        std::string pseudonym;
        if (study_id_.empty()) {
            pseudonym = generate_research_pseudonym(identifier);
        } else {
            pseudonym = generate_study_pseudonym(identifier, study_id_);
        }
        
        result.anonymized_value = pseudonym;
        result.metadata["study_id"] = study_id_;
        result.metadata["time_limited"] = time_limited_;
        result.metadata["pseudonym_length"] = static_cast<int>(pseudonym_length_);
        
        if (time_limited_) {
            auto expiry_time = std::chrono::system_clock::now() + pseudonym_expiry_;
            result.metadata["expires_at"] = std::chrono::system_clock::to_time_t(expiry_time);
        }
        
        result.audit_log = create_audit_log("RESEARCH_ID", true);
        
    } catch (const std::exception& e) {
        result.anonymized_value = std::string("PSEUDO_FAILED");
        result.metadata["error"] = e.what();
        result.audit_log = create_audit_log("RESEARCH_ID", false);
    }
    
    return result;
}

std::string ResearchPseudonymizer::generate_research_pseudonym(
    const std::string& identifier) const {
    
    std::string salt = research_key_ + "RESEARCH_STUDY";
    
    if (time_limited_) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto rotation_period = std::chrono::duration_cast<std::chrono::hours>(pseudonym_expiry_).count();
        auto time_block = time_t / (rotation_period * 3600); // Rotate based on expiry period
        salt += std::to_string(time_block);
    }
    
    // Generate hash
    unsigned char hash[32];
    if (PKCS5_PBKDF2_HMAC(identifier.c_str(), static_cast<int>(identifier.length()),
                         reinterpret_cast<const unsigned char*>(salt.c_str()),
                         static_cast<int>(salt.length()),
                         50000, EVP_sha256(), 32, hash) != 1) {
        throw common::TransformationException(
            "Research pseudonym generation failed", get_type(), "generate_research_pseudonym");
    }
    
    // Convert first bytes to pseudonym format
    uint64_t pseudonym_value = 0;
    for (int i = 0; i < 8; ++i) {
        pseudonym_value = (pseudonym_value << 8) | hash[i];
    }
    
    // Apply format
    char formatted_pseudonym[64];
    std::snprintf(formatted_pseudonym, sizeof(formatted_pseudonym), 
                 pseudonym_format_.c_str(), pseudonym_value);
    
    return std::string(formatted_pseudonym);
}

std::string ResearchPseudonymizer::generate_study_pseudonym(
    const std::string& identifier, const std::string& study_id) const {
    
    std::string salt = research_key_ + study_id;
    
    if (time_limited_) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto rotation_period = std::chrono::duration_cast<std::chrono::hours>(pseudonym_expiry_).count();
        auto time_block = time_t / (rotation_period * 3600);
        salt += std::to_string(time_block);
    }
    
    // Generate study-specific hash
    unsigned char hash[32];
    if (PKCS5_PBKDF2_HMAC(identifier.c_str(), static_cast<int>(identifier.length()),
                         reinterpret_cast<const unsigned char*>(salt.c_str()),
                         static_cast<int>(salt.length()),
                         75000, EVP_sha256(), 32, hash) != 1) {
        throw common::TransformationException(
            "Study pseudonym generation failed", get_type(), "generate_study_pseudonym");
    }
    
    // Create study-specific pseudonym
    uint64_t pseudonym_value = 0;
    for (int i = 0; i < 8; ++i) {
        pseudonym_value = (pseudonym_value << 8) | hash[i];
    }
    
    // Format with configured pseudonym format
    char formatted_pseudonym[64];
    std::snprintf(formatted_pseudonym, sizeof(formatted_pseudonym), 
                 pseudonym_format_.c_str(), pseudonym_value);
    
    return std::string(formatted_pseudonym);
}

// CompositeAnonymizer implementation

CompositeAnonymizer::CompositeAnonymizer() 
    : AnonymizationTransformation("Composite_Anonymizer"),
      overall_compliance_level_(AnonymizationLevel::FULLY_ANONYMIZED),
      overall_contains_personal_data_(false),
      overall_is_reversible_(false) {
}

bool CompositeAnonymizer::validate_input(const std::any& input) const {
    // Can handle any structured input (JSON, Record, etc.)
    return true;
}

void CompositeAnonymizer::configure(const YAML::Node& params) {
    config_ = params;
    
    if (params["field_anonymizers"]) {
        for (const auto& field_config : params["field_anonymizers"]) {
            std::string field_name = field_config.first.as<std::string>();
            std::string anonymizer_type = field_config.second["type"].as<std::string>();
            
            auto anonymizer = AnonymizationTransformationFactory::create(
                anonymizer_type, field_config.second);
            
            add_field_anonymizer(field_name, std::move(anonymizer));
        }
    }
}

AnonymizationLevel CompositeAnonymizer::get_compliance_level() const {
    return overall_compliance_level_;
}

bool CompositeAnonymizer::contains_personal_data() const {
    return overall_contains_personal_data_;
}

bool CompositeAnonymizer::is_reversible() const {
    return overall_is_reversible_;
}

void CompositeAnonymizer::add_field_anonymizer(
    const std::string& field_name,
    std::unique_ptr<AnonymizationTransformation> anonymizer) {
    
    // Update overall compliance based on weakest link
    auto compliance = anonymizer->get_compliance_level();
    if (static_cast<int>(compliance) > static_cast<int>(overall_compliance_level_)) {
        overall_compliance_level_ = compliance;
    }
    
    if (anonymizer->contains_personal_data()) {
        overall_contains_personal_data_ = true;
    }
    
    if (anonymizer->is_reversible()) {
        overall_is_reversible_ = true;
    }
    
    field_anonymizers_[field_name] = std::move(anonymizer);
}

AnonymizationResult CompositeAnonymizer::apply_anonymization(
    const std::any& input, core::ProcessingContext& context) {
    
    AnonymizationResult result;
    result.method_used = "composite_anonymization";
    result.compliance_level = overall_compliance_level_;
    result.contains_personal_data = overall_contains_personal_data_;
    result.is_reversible = overall_is_reversible_;
    
    try {
        nlohmann::json output_json;
        nlohmann::json metadata_json;
        
        // Handle different input types
        if (input.type() == typeid(nlohmann::json)) {
            auto input_json = std::any_cast<nlohmann::json>(input);
            
            for (const auto& [field_name, anonymizer] : field_anonymizers_) {
                if (input_json.contains(field_name)) {
                    auto transform_result = anonymizer->transform_detailed(
                        input_json[field_name], context);
                    
                    // Convert TransformationResult to AnonymizationResult for compatibility
                    AnonymizationResult field_result;
                    if (transform_result.is_success()) {
                        field_result.anonymized_value = transform_result.value;
                        field_result.method_used = anonymizer->get_type();
                        field_result.compliance_level = anonymizer->get_compliance_level();
                        field_result.contains_personal_data = anonymizer->contains_personal_data();
                        field_result.is_reversible = anonymizer->is_reversible();
                    }
                    
                    if (field_result.anonymized_value.has_value()) {
                        // Convert std::any back to JSON
                        if (field_result.anonymized_value.type() == typeid(nlohmann::json)) {
                            output_json[field_name] = std::any_cast<nlohmann::json>(
                                field_result.anonymized_value);
                        } else if (field_result.anonymized_value.type() == typeid(std::string)) {
                            output_json[field_name] = std::any_cast<std::string>(
                                field_result.anonymized_value);
                        } else {
                            output_json[field_name] = "UNSUPPORTED_TYPE";
                        }
                        
                        metadata_json[field_name] = {
                            {"method", field_result.method_used},
                            {"compliance_level", static_cast<int>(field_result.compliance_level)},
                            {"contains_personal_data", field_result.contains_personal_data},
                            {"is_reversible", field_result.is_reversible}
                        };
                    }
                }
            }
            
            // Copy non-anonymized fields
            for (auto& [key, value] : input_json.items()) {
                if (field_anonymizers_.find(key) == field_anonymizers_.end()) {
                    output_json[key] = value;
                }
            }
        } else {
            // For non-JSON types, apply generic anonymization
            output_json["anonymized_data"] = "GENERIC_ANONYMIZATION_APPLIED";
        }
        
        result.anonymized_value = output_json;
        result.metadata["field_methods"] = metadata_json;
        result.metadata["fields_processed"] = static_cast<int>(field_anonymizers_.size());
        result.audit_log = create_audit_log("COMPOSITE", true);
        
    } catch (const std::exception& e) {
        nlohmann::json error_result;
        error_result["error"] = "COMPOSITE_ANONYMIZATION_FAILED";
        
        result.anonymized_value = error_result;
        result.metadata["error"] = e.what();
        result.audit_log = create_audit_log("COMPOSITE", false);
    }
    
    return result;
}

// Factory implementation

std::unique_ptr<AnonymizationTransformation> AnonymizationTransformationFactory::create(
    const std::string& type, const YAML::Node& config) {
    
    std::unique_ptr<AnonymizationTransformation> transformation;
    
    if (type == "nhs_number_anonymizer") {
        transformation = std::make_unique<NHSNumberAnonymizer>();
    } else if (type == "date_to_age_anonymizer") {
        transformation = std::make_unique<DateToAgeAnonymizer>();
    } else if (type == "postcode_to_region_anonymizer") {
        transformation = std::make_unique<PostcodeToRegionAnonymizer>();
    } else if (type == "secure_hash_anonymizer") {
        transformation = std::make_unique<SecureHashAnonymizer>();
    } else if (type == "research_pseudonymizer") {
        transformation = std::make_unique<ResearchPseudonymizer>();
    } else if (type == "composite_anonymizer") {
        transformation = std::make_unique<CompositeAnonymizer>();
    } else {
        throw common::TransformationException(
            "Unknown anonymization transformation type: " + type,
            "AnonymizationTransformationFactory", "create");
    }
    
    if (!config.IsNull()) {
        transformation->configure(config);
    }
    
    return transformation;
}

void AnonymizationTransformationFactory::register_all_transformations() {
    auto& registry = TransformationRegistry::instance();
    
    registry.register_transformation("nhs_number_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> {
            return std::make_unique<NHSNumberAnonymizer>();
        });
    
    registry.register_transformation("date_to_age_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> {
            return std::make_unique<DateToAgeAnonymizer>();
        });
    
    registry.register_transformation("postcode_to_region_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> {
            return std::make_unique<PostcodeToRegionAnonymizer>();
        });
    
    registry.register_transformation("secure_hash_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> {
            return std::make_unique<SecureHashAnonymizer>();
        });
    
    registry.register_transformation("research_pseudonymizer",
        []() -> std::unique_ptr<FieldTransformation> {
            return std::make_unique<ResearchPseudonymizer>();
        });
    
    registry.register_transformation("composite_anonymizer",
        []() -> std::unique_ptr<FieldTransformation> {
            return std::make_unique<CompositeAnonymizer>();
        });
}

std::vector<std::string> AnonymizationTransformationFactory::get_available_types() {
    return {
        "nhs_number_anonymizer",
        "date_to_age_anonymizer", 
        "postcode_to_region_anonymizer",
        "secure_hash_anonymizer",
        "research_pseudonymizer",
        "composite_anonymizer"
    };
}

} // namespace omop::transform