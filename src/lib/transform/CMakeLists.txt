# Transform library CMakeLists.txt - Standardised version

# Validate target naming
omop_validate_target_name("omop_transform" "omop_")

# Create transform library with modern CMake approach
add_library(omop_transform STATIC)

# Define source files
set(TRANSFORM_SOURCES
    anonymization_transformations.cpp
    conditional_transformations.cpp
    numeric_transformations.cpp
    custom_transformations.cpp
    date_transformations.cpp
    field_transformations.cpp
    string_transformations.cpp
    unified_string_transformation.cpp
    transformation_engine.cpp
    transformation_utils.cpp
    validation_engine.cpp
    vocabulary_service.cpp
    vocabulary_transformations.cpp
)

# Define header files for installation
set(TRANSFORM_HEADERS
    anonymization_transformations.h
    conditional_transformations.h
    numeric_transformations.h
    custom_transformations.h
    date_transformations.h
    field_transformations.h
    string_transformations.h
    unified_string_transformation.h
    transformation_engine.h
    transformations.h
    transformation_utils.h
    validation_engine.h
    vocabulary_service.h
    vocabulary_transformations.h
    transformation_registry_improvements.h
    transformation_result.h
)

# Add source files using modern CMake
target_sources(omop_transform PRIVATE ${TRANSFORM_SOURCES})

# Configure library using standardised function
omop_configure_library(omop_transform
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_CURRENT}
    PUBLIC_DEPS
        omop_core
        omop_common
        omop_extract
        omop_ml
        yaml-cpp::yaml-cpp
    PRIVATE_DEPS
        nlohmann_json::nlohmann_json
        spdlog::spdlog
        fmt::fmt
        Threads::Threads
        ${UUID_LIBRARIES}
    HEADERS
        ${TRANSFORM_HEADERS}
)

# Add component-specific compile definitions
target_compile_definitions(omop_transform 
    PRIVATE
        OMOP_TRANSFORM_VERSION="${PROJECT_VERSION}"
        $<$<BOOL:${HAVE_LIBARCHIVE}>:OMOP_HAS_LIBARCHIVE>
        $<$<BOOL:${HAVE_GRPC}>:OMOP_HAS_GRPC>
)

# Platform-specific configurations
if(WIN32)
    target_link_libraries(omop_transform PRIVATE ${UUID_LIBRARIES})
elseif(UNIX AND NOT APPLE)
    if(UUID_LIBRARIES)
        target_link_libraries(omop_transform PRIVATE ${UUID_LIBRARIES})
    endif()
endif()

# Export component information
omop_print_component_summary("Transform" "omop_transform")

# Add alias for consistent usage
add_library(omop::transform ALIAS omop_transform)