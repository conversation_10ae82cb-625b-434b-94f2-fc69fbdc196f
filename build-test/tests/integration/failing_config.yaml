# Configuration for testing job failure scenarios
pipeline:
  batch_size: 1000
  max_parallel_batches: 2
  error_threshold: 0.01
  stop_on_error: false
  enable_checkpointing: true
  checkpoint_dir: "/tmp/omop-etl/checkpoints"
  stages:
    - name: "extract"
      type: "extractor"
      config:
        type: "csv"
        file_path: "tests/integration/test_data/csv/patients.csv"
        delimiter: ","
        has_header: true
    - name: "transform"
      type: "transformer"
      config:
        type: "failing"
        validate_records: true
    - name: "load"
      type: "loader"
      config:
        type: "failing"
        connection_string: "postgresql://test_user:test_pass@localhost:5432/test_db"
        batch_size: 1000 