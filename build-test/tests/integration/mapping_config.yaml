# OMOP ETL Mapping Configuration for Integration Tests
version: "1.0"

source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_source_db
  username: test_user
  password: test_pass

target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test_omop_db
  username: omop_user
  password: omop_pass

table_mappings:
  - source_table: patients_test
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: Gender
          mappings:
            "M": 8507
            "F": 8532

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        parameters:
          input_format: "%Y-%m-%d"
          output_format: "%Y-%m-%d %H:%M:%S"
          add_time: true
          default_time: "00:00:00"

      - source_columns: [birth_date]
        target_column: year_of_birth
        type: date_calculation
        parameters:
          extract: year

      - source_column: race
        target_column: race_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: Race
          default_concept_id: 0

      - source_column: ethnicity
        target_column: ethnicity_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: Ethnicity
          default_concept_id: 0

  - source_table: conditions_test
    target_table: condition_occurrence
    transformations:
      - source_column: condition_id
        target_column: condition_occurrence_id
        type: direct

      - source_column: patient_id
        target_column: person_id
        type: direct

      - source_column: condition_code
        target_column: condition_concept_id
        type: vocabulary_mapping
        parameters:
          vocabulary: ICD10CM
          target_vocabulary: SNOMED

      - source_column: condition_start_date
        target_column: condition_start_datetime
        type: date_transform
        parameters:
          input_format: "%Y-%m-%d"
          output_format: "%Y-%m-%d %H:%M:%S"

vocabulary_mappings:
  Gender:
    case_sensitive: false
    mappings:
      - source_value: "M"
        target_concept_id: 8507
        target_vocabulary: Gender
      - source_value: "Male"
        target_concept_id: 8507
        target_vocabulary: Gender
      - source_value: "F"
        target_concept_id: 8532
        target_vocabulary: Gender
      - source_value: "Female"
        target_concept_id: 8532
        target_vocabulary: Gender

  Race:
    case_sensitive: false
    default_concept_id: 0
    mappings:
      - source_value: "White"
        target_concept_id: 8527
      - source_value: "Black or African American"
        target_concept_id: 8516
      - source_value: "Asian"
        target_concept_id: 8515

  Ethnicity:
    case_sensitive: false
    default_concept_id: 0
    mappings:
      - source_value: "Hispanic or Latino"
        target_concept_id: 38003563
      - source_value: "Not Hispanic or Latino"
        target_concept_id: 38003564

etl_settings:
  batch_size: 1000
  max_parallel_batches: 4
  validate_records: true
  continue_on_error: false
  error_threshold: 0.01
  checkpoint_interval: 300