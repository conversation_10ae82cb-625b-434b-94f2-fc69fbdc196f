# This is a generated file and its contents are an internal implementation detail.
# The download step will be re-executed if anything in this file changes.
# No other meaning or use of this file is supported.

method=git
command=/opt/homebrew/bin/cmake;-DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE;-P;/Users/<USER>/uclwork/etl/omop-etl/build-test/_deps/fmt-subbuild/fmt-populate-prefix/tmp/fmt-populate-gitclone.cmake
source_dir=/Users/<USER>/uclwork/etl/omop-etl/build-test/_deps/fmt-src
work_dir=/Users/<USER>/uclwork/etl/omop-etl/build-test/_deps
repository=https://github.com/fmtlib/fmt.git
remote=origin
init_submodules=TRUE
recurse_submodules=--recursive
submodules=
CMP0097=NEW
      
