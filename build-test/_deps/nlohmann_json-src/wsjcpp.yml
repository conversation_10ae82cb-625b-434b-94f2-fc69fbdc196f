wsjcpp_version: "v0.1.1"
cmake_minimum_required: "3.0"
cmake_cxx_standard: "11"
name: "nlohmann/json"
version: "v3.11.2"
description: "JSON for Modern C++"
issues: "https://github.com/nlohmann/json/issues"
keywords:
  - "c++"
  - "json"

repositories:
  - type: main
    url: "https://github.com/nlohmann/json"

authors:
  - name: "<PERSON><PERSON>"
    email: "<EMAIL>"

distribution:
  - source-file: "single_include/nlohmann/json.hpp"
    target-file: "json.hpp"
    type: "source-code"
  - source-file: "single_include/nlohmann/json_fwd.hpp"
    target-file: "json_fwd.hpp"
    type: "source-code"
