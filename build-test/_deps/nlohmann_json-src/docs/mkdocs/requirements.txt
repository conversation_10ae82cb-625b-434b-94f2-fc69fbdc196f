Babel==2.10.3
certifi==2022.6.15
charset-normalizer==2.1.0
click==8.1.3
csscompressor==0.9.5
future==0.18.2
ghp-import==2.1.0
gitdb==4.0.9
GitPython==3.1.27
htmlmin==0.1.12
httplib2==0.20.4
idna==3.3
importlib-metadata==4.12.0
Jinja2==3.1.2
joblib==1.1.0
jsmin==3.0.1
livereload==2.6.3
lunr==0.6.2
Markdown==3.3.0                 # pinned due to version conflict with markdown-include and mkdocs
markdown-include==0.7.0
MarkupSafe==2.1.1
mergedeep==1.3.4
mkdocs==1.3.1
mkdocs-git-revision-date-localized-plugin==1.1.0
mkdocs-material==8.3.9
mkdocs-material-extensions==1.0.3
mkdocs-minify-plugin==0.5.0
mkdocs-redirects==1.0.5
mkdocs-simple-hooks==0.1.5
nltk==3.7
packaging==21.3
plantuml==0.3.0
plantuml-markdown==3.6.3
Pygments==2.12.0
pymdown-extensions==9.5
pyparsing==3.0.9
python-dateutil==2.8.2
pytz==2022.1
PyYAML==6.0
pyyaml_env_tag==0.1
regex==2022.7.25
requests==2.28.1
six==1.16.0
smmap==5.0.0
tornado==6.2
tqdm==4.64.0
urllib3==1.26.11
watchdog==2.1.9
zipp==3.8.1
