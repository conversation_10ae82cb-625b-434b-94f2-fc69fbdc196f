# OMOP ETL Pipeline - CLI Commands and API Documentation

## Overview

This guide provides comprehensive documentation for interacting with the OMOP ETL Pipeline through both Command Line Interface (CLI) and REST API. It includes complete end-to-end testing scenarios and practical examples for connecting to and managing ETL processes.

## Table of Contents

1. [CLI Commands Reference](#cli-commands-reference)
2. [REST API Documentation](#rest-api-documentation)
3. [End-to-End Testing Scenarios](#end-to-end-testing-scenarios)
4. [Configuration Examples](#configuration-examples)
5. [Authentication and Security](#authentication-and-security)
6. [Monitoring and Troubleshooting](#monitoring-and-troubleshooting)

---

## CLI Commands Reference

### Installation and Setup

```bash
# Build the CLI tool using Docker
./scripts/build.sh build -t cli

# Or run directly in development environment
./scripts/build.sh dev
```

### Core ETL Commands

#### 1. Run Complete ETL Pipeline

```bash
# Basic ETL run for a specific table
omop-etl run person --config config/etl/mysql_mappings.yaml

# Run with custom parameters
omop-etl run person \
  --config config/etl/mysql_mappings.yaml \
  --batch-size 5000 \
  --parallel-jobs 4 \
  --source "patients_2024"

# Dry run (validation only, no data loading)
omop-etl run person \
  --config config/etl/mysql_mappings.yaml \
  --dry-run \
  --verbose

# Run all configured tables
omop-etl run-all \
  --config config/etl/mysql_mappings.yaml \
  --parallel \
  --max-concurrent 3
```

#### 2. Configuration Management

```bash
# Validate configuration file
omop-etl validate --config config/etl/mysql_mappings.yaml --verbose

# Generate sample configuration
omop-etl generate-config \
  --source mysql \
  --target postgresql \
  --tables person,visit_occurrence,condition_occurrence \
  > my-config.yaml

# Test database connections
omop-etl test-connection --config config/etl/mysql_mappings.yaml

# Export configuration schema
omop-etl export-schema --format json > schema.json
```

#### 3. Job Management

```bash
# Submit job to run in background
omop-etl submit \
  --config config/etl/mysql_mappings.yaml \
  --name "Daily Patient Import" \
  --priority high \
  --table person

# Check job status
omop-etl status --job-id abc123-def456-789

# List all jobs
omop-etl list-jobs --limit 10 --state running

# List jobs with filtering
omop-etl list-jobs \
  --state completed \
  --since "2024-01-01" \
  --table person

# Cancel running job
omop-etl cancel --job-id abc123-def456-789

# Retry failed job
omop-etl retry --job-id abc123-def456-789 --max-retries 3
```

#### 4. System Management

```bash
# List available components
omop-etl list-extractors
omop-etl list-transformers
omop-etl list-loaders

# Create OMOP CDM schema
omop-etl create-schema \
  --config config/etl/mysql_mappings.yaml \
  --drop-existing \
  --tables person,visit_occurrence,condition_occurrence

# System health check
omop-etl health-check --config config/etl/mysql_mappings.yaml

# Get system statistics
omop-etl stats --format json
```

#### 5. Development and Debugging

```bash
# Run with debug logging
omop-etl run person \
  --config config/etl/mysql_mappings.yaml \
  --log-level debug \
  --debug-sql

# Profile performance
omop-etl run person \
  --config config/etl/mysql_mappings.yaml \
  --profile \
  --profile-output profile.json

# Resume from checkpoint
omop-etl resume --job-id abc123-def456-789

# Reprocess failed records only
omop-etl reprocess \
  --job-id abc123-def456-789 \
  --failed-only \
  --batch-size 1000
```

### CLI Global Options

```bash
# Global options available for all commands
omop-etl [command] \
  --config /path/to/config.yaml \     # Configuration file path
  --verbose \                         # Enable verbose output
  --quiet \                          # Suppress output except errors
  --log-level debug \                # Set logging level (debug, info, warn, error)
  --log-file /path/to/log.txt \      # Log to file
  --no-color \                       # Disable colored output
  --help \                           # Show help
  --version                          # Show version
```

---

## REST API Documentation

### Starting the API Server

```bash
# Start with default settings
omop-etl-api

# Start with custom configuration
omop-etl-api \
  --config config/api/config.yaml \
  --port 8080 \
  --host 0.0.0.0

# Start in development mode
omop-etl-api --dev --log-level debug --cors-enabled
```

### API Base URL and Versioning

```
Base URL: http://localhost:8080/api/v1
Content-Type: application/json
Authentication: Bearer Token or API Key
```

### Core API Endpoints

#### 1. Job Management

**Create ETL Job**
```bash
curl -X POST http://localhost:8080/api/v1/etl/jobs \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Patient Data Import",
    "description": "Import patient data from MySQL to OMOP CDM",
    "config_path": "config/etl/mysql_mappings.yaml",
    "table": "person",
    "priority": "high",
    "max_retries": 3,
    "retry_delay_seconds": 60,
    "enable_checkpointing": true,
    "parameters": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31",
      "batch_size": 10000,
      "parallel_threads": 4
    }
  }'
```

**Get Job Status**
```bash
curl -X GET http://localhost:8080/api/v1/etl/jobs/{job_id} \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**List Jobs**
```bash
# List all jobs
curl -X GET http://localhost:8080/api/v1/etl/jobs \
  -H "Authorization: Bearer ${API_TOKEN}"

# List with filtering
curl -X GET "http://localhost:8080/api/v1/etl/jobs?state=running&limit=10&offset=0" \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**Cancel Job**
```bash
curl -X DELETE http://localhost:8080/api/v1/etl/jobs/{job_id} \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**Retry Job**
```bash
curl -X POST http://localhost:8080/api/v1/etl/jobs/{job_id}/retry \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"max_retries": 3}'
```

#### 2. Job Monitoring

**Get Job Logs**
```bash
# Get complete logs
curl -X GET http://localhost:8080/api/v1/etl/jobs/{job_id}/logs \
  -H "Authorization: Bearer ${API_TOKEN}"

# Stream logs in real-time
curl -N -X GET "http://localhost:8080/api/v1/etl/jobs/{job_id}/logs?follow=true" \
  -H "Authorization: Bearer ${API_TOKEN}"

# Get logs with filtering
curl -X GET "http://localhost:8080/api/v1/etl/jobs/{job_id}/logs?level=error&since=2024-01-01T00:00:00Z" \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**Get Job Statistics**
```bash
curl -X GET http://localhost:8080/api/v1/etl/jobs/{job_id}/stats \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**Get Job Progress**
```bash
curl -X GET http://localhost:8080/api/v1/etl/jobs/{job_id}/progress \
  -H "Authorization: Bearer ${API_TOKEN}"
```

#### 3. Configuration Management

**Validate Configuration**
```bash
curl -X POST http://localhost:8080/api/v1/config/validate \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "config_path": "config/etl/mysql_mappings.yaml"
  }'
```

**Get Configuration Schema**
```bash
curl -X GET http://localhost:8080/api/v1/config/schema \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**Test Database Connection**
```bash
curl -X POST http://localhost:8080/api/v1/config/test-connection \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "connection": {
      "type": "mysql",
      "host": "localhost",
      "port": 3306,
      "database": "clinical_db",
      "username": "user",
      "password": "password"
    }
  }'
```

#### 4. System Information

**Health Check**
```bash
curl -X GET http://localhost:8080/api/v1/health \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**System Metrics**
```bash
curl -X GET http://localhost:8080/api/v1/metrics \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**System Version**
```bash
curl -X GET http://localhost:8080/api/v1/version \
  -H "Authorization: Bearer ${API_TOKEN}"
```

**List Available Components**
```bash
# List extractors
curl -X GET http://localhost:8080/api/v1/components/extractors \
  -H "Authorization: Bearer ${API_TOKEN}"

# List transformers
curl -X GET http://localhost:8080/api/v1/components/transformers \
  -H "Authorization: Bearer ${API_TOKEN}"

# List loaders
curl -X GET http://localhost:8080/api/v1/components/loaders \
  -H "Authorization: Bearer ${API_TOKEN}"
```

---

## End-to-End Testing Scenarios

### Scenario 1: Complete MySQL to OMOP CDM Migration

This scenario demonstrates a complete end-to-end ETL process from MySQL clinical database to OMOP CDM PostgreSQL database.

#### Step 1: Environment Setup

```bash
# 1. Start the development environment
./scripts/build.sh dev -e dev

# 2. Start required services (MySQL source, PostgreSQL target)
./scripts/build.sh up --profiles "mysql,postgres"

# 3. Load test data into MySQL
docker-compose exec mysql mysql -u root -p clinical_db < tests/data/sample_clinical_data.sql

# 4. Verify OMOP CDM schema exists
docker-compose exec postgres psql -U omop_user -d omop_cdm -c "\dt"
```

#### Step 2: Configuration Validation

```bash
# Validate the configuration file
omop-etl validate --config config/etl/mysql_mappings.yaml --verbose

# Test database connections
omop-etl test-connection --config config/etl/mysql_mappings.yaml

# Expected output:
# ✓ Source MySQL connection: OK
# ✓ Target PostgreSQL connection: OK
# ✓ Configuration validation: PASSED
```

#### Step 3: Start API Server

```bash
# Start the API server in background
omop-etl-api --config config/api/config.yaml --port 8080 &

# Verify API is running
curl -X GET http://localhost:8080/api/v1/health
```

#### Step 4: Execute Complete ETL Pipeline

**Option A: Using CLI**
```bash
# Run complete pipeline for all tables
omop-etl run-all \
  --config config/etl/mysql_mappings.yaml \
  --parallel \
  --max-concurrent 3 \
  --verbose

# Monitor progress
omop-etl list-jobs --state running
```

**Option B: Using REST API**
```bash
# Submit jobs for each OMOP table
for table in person visit_occurrence condition_occurrence procedure_occurrence measurement observation; do
  curl -X POST http://localhost:8080/api/v1/etl/jobs \
    -H "Authorization: Bearer ${API_TOKEN}" \
    -H "Content-Type: application/json" \
    -d "{
      \"name\": \"ETL Job - ${table}\",
      \"config_path\": \"config/etl/mysql_mappings.yaml\",
      \"table\": \"${table}\",
      \"priority\": \"normal\",
      \"parameters\": {
        \"batch_size\": 5000,
        \"parallel_threads\": 2
      }
    }"
done
```

#### Step 5: Monitor and Validate Results

```bash
# Check job statuses
curl -X GET http://localhost:8080/api/v1/etl/jobs \
  -H "Authorization: Bearer ${API_TOKEN}" | jq '.jobs[] | {name, state, progress}'

# Validate data in OMOP CDM
docker-compose exec postgres psql -U omop_user -d omop_cdm -c "
  SELECT
    'person' as table_name, COUNT(*) as record_count
  FROM person
  UNION ALL
  SELECT
    'visit_occurrence' as table_name, COUNT(*) as record_count
  FROM visit_occurrence
  UNION ALL
  SELECT
    'condition_occurrence' as table_name, COUNT(*) as record_count
  FROM condition_occurrence;
"
```

### Scenario 2: CSV File Processing with Data Quality Validation

This scenario processes CSV files with comprehensive data quality checks.

#### Step 1: Prepare Test Data

```bash
# Create test CSV files
mkdir -p tests/data/csv_input

# Generate sample patient data
cat > tests/data/csv_input/patients.csv << 'EOF'
patient_id,first_name,last_name,birth_date,gender,race,ethnicity,zip_code
1001,John,Doe,1985-03-15,M,White,Not Hispanic,12345
1002,Jane,Smith,1990-07-22,F,Black,Hispanic,12346
1003,Bob,Johnson,1975-11-08,M,Asian,Not Hispanic,12347
EOF

# Generate sample visit data
cat > tests/data/csv_input/visits.csv << 'EOF'
visit_id,patient_id,visit_start_date,visit_end_date,visit_type,provider_id
2001,1001,2024-01-15,2024-01-15,Outpatient,501
2002,1002,2024-01-16,2024-01-18,Inpatient,502
2003,1003,2024-01-17,2024-01-17,Emergency,503
EOF
```

#### Step 2: Configure CSV Processing

```yaml
# config/etl/csv_test_mappings.yaml
source:
  type: csv
  file_path: "tests/data/csv_input/"
  options:
    delimiter: ","
    has_header: true
    encoding: "UTF-8"

target:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: omop_cdm
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}

job_config:
  batch_size: 1000
  enable_validation: true
  validation_threshold: 0.95
  enable_checkpointing: true

mappings:
  person:
    source_file: "patients.csv"
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
        validation:
          required: true
          data_type: integer

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        input_format: "%Y-%m-%d"
        output_format: "%Y-%m-%d %H:%M:%S"
        validation:
          required: true
          min_date: "1900-01-01"
          max_date: "2024-12-31"

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507
          "F": 8532
        validation:
          required: true
          valid_concept_ids: [8507, 8532]

    validation_rules:
      - field: person_id
        type: required
      - field: birth_datetime
        type: date_format
        format: "%Y-%m-%d %H:%M:%S"
      - field: gender_concept_id
        type: range
        min: 1
        max: 9999999
```

#### Step 3: Execute CSV Processing

```bash
# Process CSV files with validation
omop-etl run person \
  --config config/etl/csv_test_mappings.yaml \
  --verbose \
  --log-level debug

# Check validation results
omop-etl stats --format json | jq '.validation_results'
```

### Scenario 3: Real-time Monitoring and Error Handling

This scenario demonstrates monitoring capabilities and error recovery.

#### Step 1: Submit Long-running Job

```bash
# Submit a large dataset processing job
JOB_ID=$(curl -s -X POST http://localhost:8080/api/v1/etl/jobs \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Large Dataset Processing",
    "config_path": "config/etl/mysql_mappings.yaml",
    "table": "measurement",
    "parameters": {
      "batch_size": 1000,
      "parallel_threads": 1
    }
  }' | jq -r '.job_id')

echo "Job ID: $JOB_ID"
```

#### Step 2: Real-time Monitoring

```bash
# Monitor job progress in real-time
watch -n 5 "curl -s -X GET http://localhost:8080/api/v1/etl/jobs/$JOB_ID \
  -H 'Authorization: Bearer ${API_TOKEN}' | jq '.progress'"

# Stream logs in another terminal
curl -N -X GET "http://localhost:8080/api/v1/etl/jobs/$JOB_ID/logs?follow=true" \
  -H "Authorization: Bearer ${API_TOKEN}"
```

#### Step 3: Error Simulation and Recovery

```bash
# Simulate database connection failure (stop target database)
docker-compose stop postgres

# Wait for job to fail
sleep 30

# Check job status
curl -X GET http://localhost:8080/api/v1/etl/jobs/$JOB_ID \
  -H "Authorization: Bearer ${API_TOKEN}" | jq '.state'

# Restart database
docker-compose start postgres

# Retry the failed job
curl -X POST http://localhost:8080/api/v1/etl/jobs/$JOB_ID/retry \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"max_retries": 3}'
```

### Scenario 4: Performance Testing and Optimization

This scenario tests system performance with large datasets.

#### Step 1: Generate Large Test Dataset

```bash
# Generate large CSV file for performance testing
python3 << 'EOF'
import csv
import random
from datetime import datetime, timedelta

# Generate 100,000 patient records
with open('tests/data/large_patients.csv', 'w', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(['patient_id', 'birth_date', 'gender', 'race_code'])

    for i in range(1, 100001):
        birth_date = datetime(1920, 1, 1) + timedelta(days=random.randint(0, 36500))
        gender = random.choice(['M', 'F'])
        race_code = random.choice(['1', '2', '3', '4', '5'])
        writer.writerow([i, birth_date.strftime('%Y-%m-%d'), gender, race_code])

print("Generated 100,000 patient records")
EOF
```

#### Step 2: Performance Testing

```bash
# Test with different batch sizes
for batch_size in 1000 5000 10000 20000; do
  echo "Testing batch size: $batch_size"

  start_time=$(date +%s)

  omop-etl run person \
    --config config/etl/csv_large_test.yaml \
    --batch-size $batch_size \
    --parallel-jobs 4 \
    --profile

  end_time=$(date +%s)
  duration=$((end_time - start_time))

  echo "Batch size $batch_size completed in $duration seconds"
done
```

#### Step 3: Monitor System Resources

```bash
# Monitor system resources during ETL
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# Get detailed performance metrics
curl -X GET http://localhost:8080/api/v1/metrics \
  -H "Authorization: Bearer ${API_TOKEN}" | jq '.performance_metrics'
```

---

## Configuration Examples

### Complete MySQL to OMOP Configuration

```yaml
# config/etl/mysql_complete_mappings.yaml
metadata:
  name: "Complete MySQL to OMOP CDM ETL"
  version: "1.0.0"
  description: "Full ETL pipeline for clinical data transformation"
  author: "UCL OMOP ETL Team"

# Source database configuration
source:
  type: mysql
  connection:
    host: ${MYSQL_HOST:-localhost}
    port: ${MYSQL_PORT:-3306}
    database: ${MYSQL_DB:-clinical_db}
    username: ${MYSQL_USER}
    password: ${MYSQL_PASSWORD}
    pool_size: 10
    connection_timeout: 30
    charset: utf8mb4

  # Connection validation
  validation_query: "SELECT 1"
  test_on_borrow: true

# Target OMOP CDM database
target:
  type: postgresql
  connection:
    host: ${OMOP_HOST:-localhost}
    port: ${OMOP_PORT:-5432}
    database: ${OMOP_DB:-omop_cdm}
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}
    schema: cdm
    pool_size: 15
    connection_timeout: 60

  # Performance settings
  batch_insert_size: 10000
  use_bulk_insert: true
  disable_constraints_during_load: false

# Global job configuration
job_config:
  batch_size: 5000
  parallel_threads: 4
  max_concurrent_jobs: 3
  enable_checkpointing: true
  checkpoint_interval: 10000
  enable_validation: true
  validation_threshold: 0.95
  max_error_rate: 0.05
  retry_attempts: 3
  retry_delay_seconds: 60

# Logging configuration
logging:
  level: INFO
  format: json
  include_sql: false
  log_validation_errors: true
  log_transformation_details: false

# Performance monitoring
monitoring:
  enable_metrics: true
  metrics_interval: 30
  enable_profiling: false
  memory_threshold_mb: 8192

# Security settings
security:
  enable_encryption: true
  anonymization_salt: ${ANONYMIZATION_SALT}
  enable_audit_logging: true
  mask_sensitive_data_in_logs: true

# Table mappings with comprehensive transformations
mappings:
  person:
    source_query: |
      SELECT
        p.patient_id,
        p.first_name,
        p.last_name,
        p.birth_date,
        p.gender,
        p.race_code,
        p.ethnicity_code,
        p.zip_code,
        p.created_at,
        p.updated_at
      FROM patients p
      WHERE p.active = 1
        AND p.deleted_at IS NULL
        AND p.updated_at >= ?

    source_parameters: ["${LAST_EXTRACT_TIME:-1900-01-01}"]
    target_table: person

    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
        validation:
          required: true
          data_type: integer
          min_value: 1

      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        input_formats:
          - "%Y-%m-%d"
          - "%d/%m/%Y"
        output_format: "%Y-%m-%d %H:%M:%S"
        default_time: "00:00:00"
        validation:
          required: true
          min_date: "1900-01-01"
          max_date: "2024-12-31"

      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507      # Male
          "F": 8532      # Female
          "U": 0         # Unknown
          "NULL": 0      # No information
        default_value: 0
        validation:
          required: true
          valid_concept_ids: [8507, 8532, 0]

      - source_column: race_code
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        mapping:
          "1": 8527      # White
          "2": 8516      # Black
          "3": 8515      # Asian
          "4": 8557      # Native American
          "5": 8522      # Other
        default_value: 0
        validation:
          valid_concept_ids: [8527, 8516, 8515, 8557, 8522, 0]

      - source_column: zip_code
        target_column: location_id
        type: location_lookup
        lookup_table: location
        lookup_key: zip
        default_value: null
        validation:
          pattern: "^[0-9]{5}(-[0-9]{4})?$"

    validation_rules:
      - field: person_id
        type: required
        error_message: "Person ID is required"

      - field: birth_datetime
        type: date_range
        min_date: "1900-01-01"
        max_date: "2024-12-31"
        error_message: "Birth date must be between 1900 and 2024"

      - field: gender_concept_id
        type: concept_validation
        domain: Gender
        error_message: "Invalid gender concept ID"

      - cross_field_rule: "age_validation"
        expression: "DATEDIFF(CURDATE(), birth_datetime) / 365.25 BETWEEN 0 AND 150"
        error_message: "Age must be between 0 and 150 years"

  visit_occurrence:
    source_query: |
      SELECT
        e.encounter_id,
        e.patient_id,
        e.visit_start_date,
        e.visit_end_date,
        e.visit_type_code,
        e.provider_id,
        e.care_site_id,
        e.discharge_to_code
      FROM encounters e
      INNER JOIN patients p ON e.patient_id = p.patient_id
      WHERE e.status = 'completed'
        AND p.active = 1
        AND e.visit_start_date >= ?

    source_parameters: ["${LAST_EXTRACT_TIME:-1900-01-01}"]
    target_table: visit_occurrence

    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        validation:
          required: true
          data_type: integer

      - source_column: patient_id
        target_column: person_id
        type: direct
        validation:
          required: true
          data_type: integer
          foreign_key:
            table: person
            column: person_id

      - source_column: visit_start_date
        target_column: visit_start_datetime
        type: date_transform
        input_format: "%Y-%m-%d %H:%M:%S"
        output_format: "%Y-%m-%d %H:%M:%S"
        validation:
          required: true

      - source_column: visit_end_date
        target_column: visit_end_datetime
        type: date_transform
        input_format: "%Y-%m-%d %H:%M:%S"
        output_format: "%Y-%m-%d %H:%M:%S"
        validation:
          required: false

      - source_column: visit_type_code
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: Visit
        mapping:
          "IP": 9201      # Inpatient Visit
          "OP": 9202      # Outpatient Visit
          "ER": 9203      # Emergency Room Visit
          "LTC": 42898160 # Long Term Care Visit
        default_value: 0
        validation:
          required: true

    validation_rules:
      - cross_field_rule: "visit_date_sequence"
        expression: "visit_end_datetime IS NULL OR visit_end_datetime >= visit_start_datetime"
        error_message: "Visit end date must be after or equal to start date"

      - field: person_id
        type: foreign_key
        reference_table: person
        reference_column: person_id
        error_message: "Person ID must exist in person table"
```

---

## Authentication and Security

### API Authentication

The OMOP ETL API supports multiple authentication methods:

#### 1. Bearer Token Authentication

```bash
# Obtain API token
TOKEN=$(curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "etl_user",
    "password": "secure_password"
  }' | jq -r '.access_token')

# Use token in subsequent requests
curl -X GET http://localhost:8080/api/v1/etl/jobs \
  -H "Authorization: Bearer $TOKEN"
```

#### 2. API Key Authentication

```bash
# Set API key in environment
export API_KEY="your-api-key-here"

# Use API key in requests
curl -X GET http://localhost:8080/api/v1/etl/jobs \
  -H "X-API-Key: $API_KEY"
```

#### 3. CLI Authentication

```bash
# Login via CLI
omop-etl auth login --username etl_user --password-stdin

# Or set credentials in environment
export OMOP_ETL_USERNAME="etl_user"
export OMOP_ETL_PASSWORD="secure_password"

# Or use configuration file
omop-etl auth config --config-file ~/.omop-etl/credentials.yaml
```

### Security Configuration

```yaml
# config/api/security.yaml
security:
  authentication:
    enabled: true
    methods: ["bearer_token", "api_key"]
    token_expiry: 3600  # 1 hour
    refresh_token_expiry: 86400  # 24 hours

  authorization:
    enabled: true
    rbac_enabled: true
    default_role: "viewer"

  encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key_rotation_interval: 2592000  # 30 days

  audit_logging:
    enabled: true
    log_all_requests: true
    log_sensitive_data: false
    retention_days: 90

# Role-based access control
roles:
  admin:
    permissions:
      - "etl:*"
      - "config:*"
      - "system:*"
      - "users:*"

  etl_operator:
    permissions:
      - "etl:create"
      - "etl:read"
      - "etl:update"
      - "etl:cancel"
      - "config:read"

  viewer:
    permissions:
      - "etl:read"
      - "config:read"
      - "system:health"
```

---

## Monitoring and Troubleshooting

### System Health Monitoring

#### Health Check Endpoints

```bash
# Basic health check
curl -X GET http://localhost:8080/api/v1/health

# Detailed health check with dependencies
curl -X GET http://localhost:8080/api/v1/health/detailed

# Component-specific health checks
curl -X GET http://localhost:8080/api/v1/health/database
curl -X GET http://localhost:8080/api/v1/health/services
curl -X GET http://localhost:8080/api/v1/health/storage
```

#### Metrics Collection

```bash
# Get system metrics
curl -X GET http://localhost:8080/api/v1/metrics

# Get ETL-specific metrics
curl -X GET http://localhost:8080/api/v1/metrics/etl

# Get performance metrics
curl -X GET http://localhost:8080/api/v1/metrics/performance

# Export metrics in Prometheus format
curl -X GET http://localhost:8080/api/v1/metrics/prometheus
```

### Troubleshooting Common Issues

#### 1. Connection Issues

```bash
# Test database connectivity
omop-etl test-connection --config config/etl/mysql_mappings.yaml --verbose

# Check connection pool status
curl -X GET http://localhost:8080/api/v1/system/connection-pools

# Reset connection pools
curl -X POST http://localhost:8080/api/v1/system/connection-pools/reset
```

#### 2. Performance Issues

```bash
# Get current system load
curl -X GET http://localhost:8080/api/v1/system/load

# Get memory usage
curl -X GET http://localhost:8080/api/v1/system/memory

# Get active job statistics
curl -X GET http://localhost:8080/api/v1/etl/jobs/stats

# Enable performance profiling
omop-etl run person --config config.yaml --profile --profile-output profile.json
```

#### 3. Data Quality Issues

```bash
# Get validation error summary
curl -X GET http://localhost:8080/api/v1/etl/jobs/{job_id}/validation-errors

# Export failed records
curl -X GET http://localhost:8080/api/v1/etl/jobs/{job_id}/failed-records \
  -H "Accept: text/csv" > failed_records.csv

# Reprocess failed records
omop-etl reprocess --job-id {job_id} --failed-only --fix-errors
```

#### 4. Configuration Issues

```bash
# Validate configuration syntax
omop-etl validate --config config.yaml --strict

# Check configuration schema
omop-etl validate --config config.yaml --schema-only

# Test transformation rules
omop-etl test-transform --config config.yaml --table person --sample-size 100
```

### Log Analysis

#### CLI Log Commands

```bash
# View recent logs
omop-etl logs --tail 100

# Filter logs by level
omop-etl logs --level error --since "1 hour ago"

# Search logs
omop-etl logs --grep "validation error" --since "2024-01-01"

# Export logs
omop-etl logs --since "2024-01-01" --format json > etl_logs.json
```

#### API Log Endpoints

```bash
# Get job logs
curl -X GET "http://localhost:8080/api/v1/etl/jobs/{job_id}/logs?level=error&limit=100"

# Stream logs in real-time
curl -N -X GET "http://localhost:8080/api/v1/etl/jobs/{job_id}/logs?follow=true"

# Search logs
curl -X GET "http://localhost:8080/api/v1/logs/search?query=validation+error&since=2024-01-01"
```

### Emergency Procedures

#### System Recovery

```bash
# Stop all running jobs
curl -X POST http://localhost:8080/api/v1/system/emergency-stop

# Clear job queue
curl -X POST http://localhost:8080/api/v1/system/clear-queue

# Reset system state
curl -X POST http://localhost:8080/api/v1/system/reset

# Restart services
curl -X POST http://localhost:8080/api/v1/system/restart
```

#### Data Recovery

```bash
# Restore from checkpoint
omop-etl restore --job-id {job_id} --checkpoint {checkpoint_id}

# Rollback transaction
omop-etl rollback --job-id {job_id} --to-checkpoint {checkpoint_id}

# Repair corrupted data
omop-etl repair --table person --validate --fix-references
```

---

## Quick Reference

### Essential CLI Commands

```bash
# Start ETL for single table
omop-etl run person --config config.yaml

# Run all tables in parallel
omop-etl run-all --config config.yaml --parallel

# Monitor job progress
omop-etl status --job-id {job_id}

# Validate configuration
omop-etl validate --config config.yaml

# Test connections
omop-etl test-connection --config config.yaml
```

### Essential API Endpoints

```bash
# Submit job
POST /api/v1/etl/jobs

# Get job status
GET /api/v1/etl/jobs/{job_id}

# List jobs
GET /api/v1/etl/jobs

# Health check
GET /api/v1/health

# System metrics
GET /api/v1/metrics
```

### Environment Variables

```bash
# Database connections
export MYSQL_HOST=localhost
export MYSQL_USER=clinical_user
export MYSQL_PASSWORD=secure_password
export OMOP_HOST=localhost
export OMOP_USER=omop_user
export OMOP_PASSWORD=secure_password

# API configuration
export API_TOKEN=your-api-token
export API_BASE_URL=http://localhost:8080/api/v1

# Security
export ENCRYPTION_KEY=your-encryption-key
export ANONYMIZATION_SALT=your-salt-value
```

This comprehensive guide provides everything needed to interact with the OMOP ETL Pipeline through both CLI and API interfaces, with complete end-to-end testing scenarios and troubleshooting procedures.
```
