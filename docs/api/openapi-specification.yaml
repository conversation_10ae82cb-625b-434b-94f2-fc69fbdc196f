openapi: 3.0.3
info:
  title: OMOP ETL Pipeline API
  description: |
    REST API for the OMOP ETL Pipeline system that transforms healthcare data 
    from various sources into the OMOP Common Data Model (CDM) format.
    
    ## Features
    - Submit and manage ETL jobs
    - Monitor job progress and status
    - Configure data transformations
    - Validate configurations
    - System health monitoring
    - Performance metrics
    
    ## Authentication
    This API uses Bearer token authentication. Include your token in the Authorization header:
    ```
    Authorization: Bearer <your-token>
    ```
  version: 2.0.0
  contact:
    name: UCL OMOP ETL Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: Development server
  - url: https://etl-api.ucl.ac.uk/api/v1
    description: Production server

security:
  - bearerAuth: []
  - apiKeyAuth: []

paths:
  /health:
    get:
      summary: System health check
      description: Returns the overall health status of the ETL system
      tags:
        - System
      security: []
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
        '503':
          description: System is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'

  /health/detailed:
    get:
      summary: Detailed health check
      description: Returns detailed health information for all system components
      tags:
        - System
      responses:
        '200':
          description: Detailed health information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedHealthStatus'

  /etl/jobs:
    get:
      summary: List ETL jobs
      description: Retrieve a list of ETL jobs with optional filtering
      tags:
        - ETL Jobs
      parameters:
        - name: state
          in: query
          description: Filter jobs by state
          schema:
            type: string
            enum: [pending, running, completed, failed, cancelled]
        - name: table
          in: query
          description: Filter jobs by target table
          schema:
            type: string
        - name: limit
          in: query
          description: Maximum number of jobs to return
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 50
        - name: offset
          in: query
          description: Number of jobs to skip
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: since
          in: query
          description: Return jobs created after this timestamp
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: List of ETL jobs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobListResponse'
        '400':
          description: Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      summary: Create ETL job
      description: Submit a new ETL job for processing
      tags:
        - ETL Jobs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateJobRequest'
      responses:
        '201':
          description: Job created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobResponse'
        '400':
          description: Invalid job request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Job with same name already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /etl/jobs/{jobId}:
    get:
      summary: Get ETL job details
      description: Retrieve detailed information about a specific ETL job
      tags:
        - ETL Jobs
      parameters:
        - name: jobId
          in: path
          required: true
          description: Unique identifier of the ETL job
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Job details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobResponse'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    delete:
      summary: Cancel ETL job
      description: Cancel a running or pending ETL job
      tags:
        - ETL Jobs
      parameters:
        - name: jobId
          in: path
          required: true
          description: Unique identifier of the ETL job
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Job cancelled successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobResponse'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Job cannot be cancelled in current state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /etl/jobs/{jobId}/retry:
    post:
      summary: Retry failed ETL job
      description: Retry a failed ETL job with optional configuration changes
      tags:
        - ETL Jobs
      parameters:
        - name: jobId
          in: path
          required: true
          description: Unique identifier of the ETL job
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetryJobRequest'
      responses:
        '200':
          description: Job retry initiated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobResponse'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Job cannot be retried in current state
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /etl/jobs/{jobId}/logs:
    get:
      summary: Get job logs
      description: Retrieve logs for a specific ETL job
      tags:
        - ETL Jobs
      parameters:
        - name: jobId
          in: path
          required: true
          description: Unique identifier of the ETL job
          schema:
            type: string
            format: uuid
        - name: level
          in: query
          description: Filter logs by level
          schema:
            type: string
            enum: [debug, info, warn, error]
        - name: since
          in: query
          description: Return logs after this timestamp
          schema:
            type: string
            format: date-time
        - name: limit
          in: query
          description: Maximum number of log entries to return
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 1000
        - name: follow
          in: query
          description: Stream logs in real-time
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Job logs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LogResponse'
            text/plain:
              schema:
                type: string
                description: Raw log output
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /etl/jobs/{jobId}/progress:
    get:
      summary: Get job progress
      description: Get real-time progress information for an ETL job
      tags:
        - ETL Jobs
      parameters:
        - name: jobId
          in: path
          required: true
          description: Unique identifier of the ETL job
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Job progress information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobProgress'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /etl/jobs/{jobId}/stats:
    get:
      summary: Get job statistics
      description: Get detailed statistics for an ETL job
      tags:
        - ETL Jobs
      parameters:
        - name: jobId
          in: path
          required: true
          description: Unique identifier of the ETL job
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Job statistics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobStatistics'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    HealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
        timestamp:
          type: string
          format: date-time
        version:
          type: string
        uptime:
          type: integer
          description: System uptime in seconds

    DetailedHealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
        timestamp:
          type: string
          format: date-time
        components:
          type: object
          properties:
            database:
              $ref: '#/components/schemas/ComponentHealth'
            services:
              $ref: '#/components/schemas/ComponentHealth'
            storage:
              $ref: '#/components/schemas/ComponentHealth'

    ComponentHealth:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
        message:
          type: string
        last_check:
          type: string
          format: date-time
        response_time_ms:
          type: integer

    CreateJobRequest:
      type: object
      required:
        - name
        - config_path
        - table
      properties:
        name:
          type: string
          description: Human-readable name for the job
          example: "Daily Patient Data Import"
        description:
          type: string
          description: Optional description of the job
          example: "Import patient data from MySQL to OMOP CDM"
        config_path:
          type: string
          description: Path to the ETL configuration file
          example: "config/etl/mysql_mappings.yaml"
        table:
          type: string
          description: Target OMOP CDM table name
          example: "person"
        priority:
          type: string
          enum: [low, normal, high, critical]
          default: normal
          description: Job execution priority
        max_retries:
          type: integer
          minimum: 0
          maximum: 10
          default: 3
          description: Maximum number of retry attempts
        retry_delay_seconds:
          type: integer
          minimum: 1
          maximum: 3600
          default: 60
          description: Delay between retry attempts in seconds
        enable_checkpointing:
          type: boolean
          default: true
          description: Enable job checkpointing for recovery
        parameters:
          type: object
          description: Additional job parameters
          properties:
            start_date:
              type: string
              format: date
              description: Start date for data extraction
            end_date:
              type: string
              format: date
              description: End date for data extraction
            batch_size:
              type: integer
              minimum: 100
              maximum: 100000
              default: 5000
              description: Number of records to process per batch
            parallel_threads:
              type: integer
              minimum: 1
              maximum: 16
              default: 4
              description: Number of parallel processing threads

    RetryJobRequest:
      type: object
      properties:
        max_retries:
          type: integer
          minimum: 1
          maximum: 10
          default: 3
          description: Maximum number of retry attempts
        reset_to_checkpoint:
          type: string
          description: Checkpoint ID to reset to (optional)
        parameters:
          type: object
          description: Updated job parameters for retry

    JobResponse:
      type: object
      properties:
        job_id:
          type: string
          format: uuid
          description: Unique identifier for the job
        name:
          type: string
          description: Human-readable name for the job
        description:
          type: string
          description: Job description
        state:
          type: string
          enum: [pending, running, completed, failed, cancelled]
          description: Current job state
        config_path:
          type: string
          description: Path to the ETL configuration file
        table:
          type: string
          description: Target OMOP CDM table name
        priority:
          type: string
          enum: [low, normal, high, critical]
          description: Job execution priority
        created_at:
          type: string
          format: date-time
          description: Job creation timestamp
        started_at:
          type: string
          format: date-time
          description: Job start timestamp
        completed_at:
          type: string
          format: date-time
          description: Job completion timestamp
        progress:
          $ref: '#/components/schemas/JobProgress'
        error_message:
          type: string
          description: Error message if job failed
        retry_count:
          type: integer
          description: Number of retry attempts made
        max_retries:
          type: integer
          description: Maximum number of retry attempts allowed

    JobListResponse:
      type: object
      properties:
        jobs:
          type: array
          items:
            $ref: '#/components/schemas/JobResponse'
        total_count:
          type: integer
          description: Total number of jobs matching the query
        limit:
          type: integer
          description: Maximum number of jobs returned
        offset:
          type: integer
          description: Number of jobs skipped

    JobProgress:
      type: object
      properties:
        stage:
          type: string
          enum: [extracting, transforming, validating, loading]
          description: Current processing stage
        records_processed:
          type: integer
          description: Number of records processed so far
        total_records:
          type: integer
          description: Total number of records to process
        percentage_complete:
          type: number
          format: float
          minimum: 0
          maximum: 100
          description: Percentage of job completion
        current_batch:
          type: integer
          description: Current batch being processed
        total_batches:
          type: integer
          description: Total number of batches
        records_per_second:
          type: number
          format: float
          description: Current processing rate
        estimated_time_remaining:
          type: integer
          description: Estimated time remaining in seconds
        last_checkpoint:
          type: string
          description: ID of the last checkpoint created

    JobStatistics:
      type: object
      properties:
        job_id:
          type: string
          format: uuid
        execution_time_seconds:
          type: integer
          description: Total execution time in seconds
        records_extracted:
          type: integer
          description: Number of records extracted from source
        records_transformed:
          type: integer
          description: Number of records successfully transformed
        records_loaded:
          type: integer
          description: Number of records loaded into target
        records_failed:
          type: integer
          description: Number of records that failed processing
        validation_errors:
          type: integer
          description: Number of validation errors encountered
        transformation_errors:
          type: integer
          description: Number of transformation errors
        loading_errors:
          type: integer
          description: Number of loading errors
        data_quality_score:
          type: number
          format: float
          minimum: 0
          maximum: 1
          description: Overall data quality score (0-1)
        throughput_records_per_second:
          type: number
          format: float
          description: Average processing throughput
        memory_usage_mb:
          type: integer
          description: Peak memory usage in MB
        cpu_usage_percent:
          type: number
          format: float
          description: Average CPU usage percentage

    LogResponse:
      type: object
      properties:
        job_id:
          type: string
          format: uuid
        logs:
          type: array
          items:
            $ref: '#/components/schemas/LogEntry'
        total_count:
          type: integer
          description: Total number of log entries
        has_more:
          type: boolean
          description: Whether there are more log entries available

    LogEntry:
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          description: Log entry timestamp
        level:
          type: string
          enum: [debug, info, warn, error]
          description: Log level
        message:
          type: string
          description: Log message
        component:
          type: string
          description: Component that generated the log
        stage:
          type: string
          description: Processing stage when log was generated
        record_id:
          type: string
          description: Record ID associated with the log (if applicable)
        additional_data:
          type: object
          description: Additional structured data

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error type or code
        message:
          type: string
          description: Human-readable error message
        details:
          type: object
          description: Additional error details
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
        request_id:
          type: string
          description: Unique request identifier for tracking

tags:
  - name: System
    description: System health and information endpoints
  - name: ETL Jobs
    description: ETL job management and monitoring
  - name: Configuration
    description: Configuration management and validation
  - name: Monitoring
    description: System monitoring and metrics
