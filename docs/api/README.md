# OMOP ETL Pipeline - API and CLI Documentation

This directory contains comprehensive documentation for interacting with the OMOP ETL Pipeline through both Command Line Interface (CLI) and REST API.

## 📚 Documentation Files

### Core Documentation
- **[CLI-and-API-Guide.md](CLI-and-API-Guide.md)** - Complete guide with CLI commands, API endpoints, and end-to-end testing scenarios
- **[openapi-specification.yaml](openapi-specification.yaml)** - OpenAPI 3.0 specification for the REST API

### Supporting Files
- **[README.md](README.md)** - This overview document

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- curl and jq (for API testing)
- Access to MySQL source database and PostgreSQL OMOP CDM database

### 1. Start the System

```bash
# Clone and navigate to project
cd omop-etl

# Start required services
./scripts/build.sh dev
./scripts/build.sh up --profiles "mysql,postgres"

# Start the API server
omop-etl-api --config config/api/config.yaml --port 8080
```

### 2. Run Your First ETL Job

**Using CLI:**
```bash
# Run ETL for person table
omop-etl run person --config config/etl/mysql_mappings.yaml --verbose

# Check job status
omop-etl list-jobs --state running
```

**Using API:**
```bash
# Submit ETL job
curl -X POST http://localhost:8080/api/v1/etl/jobs \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Person Data Import",
    "config_path": "config/etl/mysql_mappings.yaml",
    "table": "person",
    "priority": "normal"
  }'

# Monitor progress
curl -X GET http://localhost:8080/api/v1/etl/jobs/{job_id} \
  -H "Authorization: Bearer ${API_TOKEN}"
```

### 3. Run End-to-End Tests

```bash
# Execute comprehensive test suite
./scripts/test-etl-pipeline.sh

# Run with custom configuration
./scripts/test-etl-pipeline.sh --config config/etl/test_mappings.yaml
```

## 📖 Detailed Documentation

### CLI Commands Reference

The CLI provides comprehensive commands for:
- **Job Management**: Submit, monitor, cancel, and retry ETL jobs
- **Configuration**: Validate configurations and test connections
- **System Management**: Health checks, metrics, and system operations
- **Development**: Debug logging, profiling, and troubleshooting

See [CLI-and-API-Guide.md](CLI-and-API-Guide.md#cli-commands-reference) for complete command reference.

### REST API Endpoints

The REST API provides programmatic access to:
- **Job Operations**: Create, monitor, and manage ETL jobs
- **Configuration Management**: Validate and test configurations
- **System Monitoring**: Health checks, metrics, and logs
- **Real-time Updates**: Streaming logs and progress monitoring

See [CLI-and-API-Guide.md](CLI-and-API-Guide.md#rest-api-documentation) for complete API reference.

### End-to-End Testing Scenarios

Comprehensive testing scenarios include:
1. **Complete MySQL to OMOP CDM Migration** - Full pipeline testing
2. **CSV File Processing** - File-based data processing with validation
3. **Real-time Monitoring** - Error handling and recovery testing
4. **Performance Testing** - Large dataset processing optimization

See [CLI-and-API-Guide.md](CLI-and-API-Guide.md#end-to-end-testing-scenarios) for detailed scenarios.

## 🔧 Configuration Examples

### Basic Configuration Structure

```yaml
# Source database
source:
  type: mysql
  connection:
    host: localhost
    port: 3306
    database: clinical_db
    username: ${MYSQL_USER}
    password: ${MYSQL_PASSWORD}

# Target OMOP CDM database
target:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: omop_cdm
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}

# Table mappings
mappings:
  person:
    source_query: "SELECT * FROM patients WHERE active = 1"
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507
          "F": 8532
```

See [CLI-and-API-Guide.md](CLI-and-API-Guide.md#configuration-examples) for complete configuration examples.

## 🔐 Authentication and Security

### API Authentication

The API supports multiple authentication methods:

```bash
# Bearer token authentication
curl -H "Authorization: Bearer ${API_TOKEN}" ...

# API key authentication
curl -H "X-API-Key: ${API_KEY}" ...
```

### CLI Authentication

```bash
# Login via CLI
omop-etl auth login --username etl_user

# Environment variables
export OMOP_ETL_USERNAME="etl_user"
export OMOP_ETL_PASSWORD="secure_password"
```

See [CLI-and-API-Guide.md](CLI-and-API-Guide.md#authentication-and-security) for complete security documentation.

## 📊 Monitoring and Troubleshooting

### Health Monitoring

```bash
# System health check
curl -X GET http://localhost:8080/api/v1/health

# Detailed component health
curl -X GET http://localhost:8080/api/v1/health/detailed

# Performance metrics
curl -X GET http://localhost:8080/api/v1/metrics
```

### Common Issues and Solutions

1. **Connection Issues**: Test database connectivity and check connection pools
2. **Performance Issues**: Monitor system load and optimize batch sizes
3. **Data Quality Issues**: Review validation errors and failed records
4. **Configuration Issues**: Validate configuration syntax and schema

See [CLI-and-API-Guide.md](CLI-and-API-Guide.md#monitoring-and-troubleshooting) for detailed troubleshooting guide.

## 🧪 Testing and Development

### Automated Testing

```bash
# Run comprehensive test suite
./scripts/test-etl-pipeline.sh

# Test specific scenarios
./scripts/test-etl-pipeline.sh --config config/etl/test_mappings.yaml

# Performance testing
./scripts/test-etl-pipeline.sh --performance-test
```

### Development Workflow

1. **Configuration Development**: Create and validate ETL configurations
2. **Local Testing**: Test with sample data using test configuration
3. **Integration Testing**: Run full pipeline with production-like data
4. **Performance Optimization**: Profile and optimize for production workloads

### Sample Test Data

The system includes sample test data for development:
- `tests/data/sample_clinical_data.sql` - MySQL test data
- `config/etl/test_mappings.yaml` - Test configuration
- `scripts/test-etl-pipeline.sh` - Automated test script

## 📋 API Reference Quick Links

### Essential Endpoints

| Operation | Method | Endpoint | Description |
|-----------|--------|----------|-------------|
| Submit Job | POST | `/api/v1/etl/jobs` | Create new ETL job |
| Get Job Status | GET | `/api/v1/etl/jobs/{id}` | Get job details |
| List Jobs | GET | `/api/v1/etl/jobs` | List all jobs |
| Cancel Job | DELETE | `/api/v1/etl/jobs/{id}` | Cancel running job |
| Health Check | GET | `/api/v1/health` | System health status |
| Metrics | GET | `/api/v1/metrics` | System metrics |

### Essential CLI Commands

| Command | Description |
|---------|-------------|
| `omop-etl run <table>` | Run ETL for specific table |
| `omop-etl run-all` | Run ETL for all configured tables |
| `omop-etl status <job-id>` | Check job status |
| `omop-etl validate --config <file>` | Validate configuration |
| `omop-etl test-connection` | Test database connections |

## 🔗 Related Documentation

- [TOGAF-AA-OMOP-ETL-Comprehensive-Data-Flow-Architecture.md](../design/architecture/TOGAF-AA-OMOP-ETL-Comprehensive-Data-Flow-Architecture.md) - Complete system architecture
- [Docker Build Guide](../../scripts/README.md) - Docker-based build and deployment
- [Configuration Reference](../../config/README.md) - Configuration file documentation

## 📞 Support and Contact

For questions, issues, or contributions:
- **Email**: <EMAIL>
- **Documentation**: This repository's docs/ directory
- **Issues**: GitHub Issues (if applicable)

---

**Note**: This documentation is designed to provide comprehensive guidance for both developers and operators working with the OMOP ETL Pipeline. For the most up-to-date information, always refer to the latest version of the documentation files.
