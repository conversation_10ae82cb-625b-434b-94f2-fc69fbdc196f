# OMOP Load Library Architecture

## Executive Summary

The OMOP Load Library provides a comprehensive data loading framework that efficiently loads transformed data into OMOP CDM target databases. The library implements various loading strategies including batch loading, incremental loading, change data capture (CDC), and data quality validation. The architecture follows TOGAF/Archi modeling specifications, organized into three main architectural domains: Business Architecture, Information Architecture, and Technology Architecture.

## Table of Contents

1. [Business Architecture](#business-architecture)
2. [Information Architecture](#information-architecture)
3. [Technology Architecture](#technology-architecture)
4. [Cross-Cutting Concerns](#cross-cutting-concerns)
5. [Deployment and Operations](#deployment-and-operations)
6. [Patterns and Anti-Patterns](#patterns-and-anti-patterns)

---

## Business Architecture

### Business Context

The Load Library serves as the data persistence layer in the OMOP ETL pipeline, enabling healthcare organizations to efficiently load transformed data into OMOP CDM target databases. The library addresses the critical business need for reliable, high-performance data loading with data quality assurance and audit capabilities.

### Business Capabilities

#### Core Loading Capabilities
- **Batch Loading**: Efficient bulk data insertion with configurable batch sizes
- **Incremental Loading**: Delta processing with change detection
- **Change Data Capture**: Real-time change tracking and synchronization
- **Data Quality Validation**: Comprehensive validation before loading
- **Audit Trail**: Complete audit trail for data loading operations
- **Error Recovery**: Robust error handling and recovery mechanisms

#### Performance Optimization
- **Parallel Loading**: Concurrent loading across multiple tables
- **Connection Pooling**: Efficient database connection management
- **Memory Optimization**: Optimized memory usage for large datasets
- **I/O Optimization**: Minimized database round trips

### Business Processes

#### Data Loading Workflow
```mermaid
graph TD
    A[Transformed Data] --> B[Pre-load Validation]
    B --> C[Loading Strategy Selection]
    C --> D[Batch Preparation]
    D --> E[Database Connection]
    E --> F[Data Insertion]
    F --> G[Post-load Validation]
    G --> H[Audit Logging]
    H --> I[Error Handling]
    I --> J[Loading Complete]
```

#### Incremental Loading Process
```mermaid
graph TD
    A[Source Data] --> B[Change Detection]
    B --> C[Delta Calculation]
    C --> D[Conflict Resolution]
    D --> E[Incremental Load]
    E --> F[Watermark Update]
    F --> G[Audit Trail]
    G --> H[Quality Check]
```

### Business Rules

#### Data Quality Rules
- **Completeness**: All required fields must be present
- **Accuracy**: Data must meet accuracy standards
- **Consistency**: Data must be consistent across related tables
- **Timeliness**: Data must be loaded within acceptable timeframes

#### Loading Rules
- **Idempotency**: Loading operations must be repeatable
- **Atomicity**: Loading operations must be atomic
- **Consistency**: Database must remain consistent after loading
- **Durability**: Loaded data must be persistent

---

## Information Architecture

### Data Models

#### Core Data Structures

```mermaid
classDiagram
    class ILoader {
        <<interface>>
        +initialize(config, context)
        +load(record, context)
        +load_batch(batch, context)
        +get_statistics()
        +finalize(context)
    }
    
    class LoaderBase {
        +initialize(config, context)
        +load(record, context)
        +load_batch(batch, context)
        +get_statistics()
        +finalize(context)
        #validate_config(config)
        #handle_error(error, context)
    }
    
    class BatchLoader {
        +size_t batch_size
        +vector<Record> batch_buffer
        +load_batch(batch, context)
        +flush_batch(context)
    }
    
    class DatabaseLoader {
        +DatabaseConnector connector
        +string table_name
        +vector<string> columns
        +load_batch(batch, context)
    }
    
    class IncrementalLoader {
        +string watermark_column
        +string watermark_value
        +load_incremental(batch, context)
        +update_watermark(value)
    }
    
    class CDCLoader {
        +string change_table
        +string change_type_column
        +load_changes(batch, context)
        +track_changes(record)
    }
    
    ILoader <|-- LoaderBase
    LoaderBase <|-- BatchLoader
    LoaderBase <|-- DatabaseLoader
    LoaderBase <|-- IncrementalLoader
    LoaderBase <|-- CDCLoader
    DatabaseLoader --> DatabaseConnector
```

#### Loading Configuration Model

```mermaid
classDiagram
    class LoadingConfiguration {
        +string table_name
        +LoadingStrategy strategy
        +size_t batch_size
        +size_t parallel_workers
        +bool enable_cdc
        +CDCConfig cdc_config
        +WatermarkInfo watermark_info
        +vector<string> tracking_columns
    }
    
    class LoadingStrategy {
        <<enumeration>>
        BATCH
        INCREMENTAL
        CDC
        MERGE
        UPSERT
    }
    
    class CDCConfig {
        +string change_table
        +string change_type_column
        +vector<string> change_columns
        +bool track_deletes
        +bool track_updates
    }
    
    class WatermarkInfo {
        +string watermark_column
        +string watermark_value
        +string watermark_type
        +bool auto_update
    }
    
    LoadingConfiguration --> LoadingStrategy
    LoadingConfiguration --> CDCConfig
    LoadingConfiguration --> WatermarkInfo
```

### Information Flow

#### Batch Loading Flow
```mermaid
graph TD
    A[Transformed Records] --> B[Batch Assembly]
    B --> C[Batch Validation]
    C --> D[Database Connection]
    D --> E[SQL Generation]
    E --> F[Batch Insert]
    F --> G[Commit Transaction]
    G --> H[Statistics Update]
    H --> I[Audit Logging]
    
    J[Error Handler] --> F
    K[Connection Pool] --> D
    L[Validation Engine] --> C
```

#### Incremental Loading Flow
```mermaid
graph TD
    A[Source Data] --> B[Watermark Check]
    B --> C[Delta Calculation]
    C --> D[Conflict Detection]
    D --> E[Conflict Resolution]
    E --> F[Incremental Load]
    F --> G[Watermark Update]
    G --> H[Audit Trail]
    
    I[Watermark Store] --> B
    I --> G
    J[Conflict Resolver] --> E
```

---

## Technology Architecture

### Component Architecture

#### Core Components

```mermaid
graph TB
    subgraph "Load Library Components"
        A[Loader Factory] --> B[Batch Loader]
        A --> C[Database Loader]
        A --> D[Incremental Loader]
        A --> E[CDC Loader]
        
        F[Loading Strategies] --> G[Batch Strategy]
        F --> H[Incremental Strategy]
        F --> I[CDC Strategy]
        F --> J[Merge Strategy]
        
        K[Connection Pool] --> L[PostgreSQL Pool]
        K --> M[MySQL Pool]
        K --> N[ODBC Pool]
        
        O[Data Quality] --> P[Pre-load Validation]
        O --> Q[Post-load Validation]
        O --> R[Data Profiling]
        
        S[Audit System] --> T[Audit Logger]
        S --> U[Change Tracking]
        S --> V[Audit Reports]
    end
    
    subgraph "External Dependencies"
        W[libpq]
        X[mysqlclient]
        Y[unixODBC]
        Z[spdlog]
    end
    
    C --> W
    C --> X
    C --> Y
    A --> Z
```

#### Current Implementation Features

**Batch Loader (`src/lib/load/batch_loader.h/cpp`):**
- Configurable batch sizes for optimal performance
- Memory-efficient batch processing
- Transaction management and rollback capabilities
- Progress monitoring and statistics collection
- Error handling and recovery mechanisms

**Database Loader (`src/lib/load/database_loader.h/cpp`):**
- Support for multiple database systems (PostgreSQL, MySQL, ODBC)
- Connection pooling for efficient resource usage
- Prepared statements for performance optimization
- Bulk insert operations with minimal round trips
- Database-specific optimizations

**Incremental Loader (`src/lib/load/loader_strategies.h/cpp`):**
- Watermark-based incremental loading
- Change detection and delta calculation
- Conflict resolution strategies
- Audit trail for incremental changes
- Performance optimization for large datasets

**CDC Loader (`src/lib/load/loader_strategies.h/cpp`):**
- Change data capture implementation
- Real-time change tracking
- Change type classification (INSERT, UPDATE, DELETE)
- Change history maintenance
- Performance optimization for change processing

**Additional Loaders (`src/lib/load/additional_loaders.h/cpp`):**
- Specialized loaders for specific use cases
- Custom loading strategies
- Integration with external systems
- Advanced data transformation during loading

### Performance Characteristics

#### Loading Performance
- **Batch Loading**: > 10,000 records/second for typical configurations
- **Incremental Loading**: > 5,000 records/second for delta processing
- **CDC Loading**: > 2,000 changes/second for change tracking
- **Memory Usage**: < 200MB for large loading operations

#### Database Performance
- **Connection Pool**: < 1ms connection acquisition
- **Batch Insert**: Optimized for minimal round trips
- **Transaction Management**: Efficient commit and rollback
- **Query Optimization**: Prepared statements and parameter binding

### Threading Model

```mermaid
graph TB
    subgraph "Loading Threading Model"
        A[Main Thread] --> B[Loader Factory]
        B --> C[Worker Threads]
        
        C --> D[Batch Workers]
        C --> E[Database Workers]
        C --> F[CDC Workers]
        
        D --> G[Connection Pool]
        E --> G
        F --> G
        
        G --> H[Database]
        H --> I[Result Aggregation]
        I --> J[Statistics Collection]
    end
    
    subgraph "Synchronization"
        K[Connection Pool Mutex]
        L[Batch Queues]
        M[Statistics Counters]
        N[Audit Logging]
    end
    
    G --> K
    D --> L
    I --> M
    J --> N
```

---

## Cross-Cutting Concerns

### Error Handling

#### Error Classification
```mermaid
classDiagram
    class LoadingException {
        <<abstract>>
        +string table_name
        +string operation
        +string error_details
    }
    
    class DatabaseException {
        +string connection_string
        +string sql_query
        +int error_code
        +string database_error
    }
    
    class ValidationException {
        +string field_name
        +string validation_rule
        +string actual_value
        +string expected_value
    }
    
    class ConflictException {
        +string conflict_type
        +string conflict_details
        +string resolution_strategy
    }
    
    LoadingException <|-- DatabaseException
    LoadingException <|-- ValidationException
    LoadingException <|-- ConflictException
```

#### Error Recovery Strategies
1. **Transaction Rollback**: Automatic rollback on database errors
2. **Retry Logic**: Exponential backoff for transient errors
3. **Partial Success**: Continue processing with failed records logged
4. **Error Aggregation**: Collect and report error patterns

### Data Quality Assurance

#### Pre-load Validation
- **Schema Validation**: Validate data against target schema
- **Business Rule Validation**: Apply business rules before loading
- **Data Type Validation**: Ensure correct data types
- **Constraint Validation**: Check foreign key and unique constraints

#### Post-load Validation
- **Row Count Validation**: Verify expected row counts
- **Data Integrity Checks**: Verify referential integrity
- **Quality Metrics**: Calculate and report quality metrics
- **Audit Trail Validation**: Verify audit trail completeness

### Security

#### Data Access Security
- **Authentication**: Database authentication with encrypted credentials
- **Authorization**: Role-based access control for loading operations
- **Encryption**: TLS/SSL for database connections
- **Audit Logging**: Complete audit trail for data access

#### Data Protection
- **Input Validation**: Validate all input data before loading
- **SQL Injection Prevention**: Use prepared statements
- **Sensitive Data Handling**: Proper handling of sensitive data
- **Access Control**: Restrict access to loading operations

### Monitoring and Observability

#### Performance Metrics
- **Loading Rate**: Records per second by table and strategy
- **Error Rate**: Error percentage by error type
- **Resource Usage**: Memory and CPU usage during loading
- **Database Performance**: Connection pool and query performance

#### Quality Metrics
- **Data Quality Scores**: Quality metrics for loaded data
- **Validation Results**: Validation pass/fail rates
- **Conflict Resolution**: Conflict detection and resolution rates
- **Audit Trail Completeness**: Audit trail coverage metrics

---

## API Documentation and Integration

### Load Library APIs

#### Core Loader Interface

```mermaid
classDiagram
    class ILoader {
        <<interface>>
        +initialize(config, context)
        +load(record, context)
        +load_batch(batch, context)
        +get_statistics()
        +finalize(context)
        +get_loader_info()
    }
    
    class LoaderBase {
        <<abstract>>
        #ProcessingContext context_
        #LoaderConfig config_
        #LoaderStatistics stats_
        +initialize(config, context)
        +get_statistics()
        #virtual load_batch_impl(batch)
        #virtual validate_batch_impl(batch)
    }
    
    class DatabaseLoader {
        +load_batch(batch, context)
        +get_statistics()
        -execute_insert(batch)
        -handle_constraint_violations()
        -manage_transactions()
    }
    
    class BatchLoader {
        +load_batch(batch, context)
        +get_statistics()
        -prepare_batch(batch)
        -execute_batch_insert()
        -handle_batch_errors()
    }
    
    class IncrementalLoader {
        +load_batch(batch, context)
        +get_statistics()
        -detect_changes(batch)
        -resolve_conflicts()
        -update_watermarks()
    }
    
    ILoader <|-- LoaderBase
    LoaderBase <|-- DatabaseLoader
    LoaderBase <|-- BatchLoader
    LoaderBase <|-- IncrementalLoader
```

#### Loader Factory API

```mermaid
classDiagram
    class LoaderFactory {
        +create_loader(type, config)
        +register_loader(type, creator)
        +list_available_loaders()
        +validate_config(type, config)
        +get_loader_info(type)
    }
    
    class LoaderCreator {
        <<interface>>
        +create(config)
    }
    
    class LoaderConfig {
        +string loader_type
        +string target_table
        +map<string, any> parameters
        +BatchConfig batch_config
        +ValidationConfig validation
        +ErrorConfig error_config
    }
    
    class LoaderInfo {
        +string name
        +string description
        +vector<string> supported_targets
        +map<string, string> required_parameters
        +map<string, string> optional_parameters
        +PerformanceProfile performance_profile
    }
    
    LoaderFactory --> LoaderCreator
    LoaderFactory --> LoaderConfig
    LoaderFactory --> LoaderInfo
```

#### Batch Processing API

```mermaid
classDiagram
    class BatchProcessor {
        +add_record(record)
        +process_batch()
        +flush_batch()
        +get_batch_size()
        +set_batch_size(size)
        +get_statistics()
    }
    
    class BatchInserter {
        +insert_batch(batch)
        +prepare_statement(sql)
        +execute_batch()
        +handle_errors()
        +get_insert_statistics()
    }
    
    class BatchValidator {
        +validate_batch(batch)
        +validate_record(record)
        +get_validation_errors()
        +clear_errors()
    }
    
    BatchProcessor --> BatchInserter
    BatchProcessor --> BatchValidator
```

### Integration Patterns

#### Batch Loading Pattern

```mermaid
graph TB
    subgraph "Batch Loading"
        A[Input Records] --> B[Batch Assembly]
        B --> C[Batch Validation]
        C --> D[Batch Preparation]
        D --> E[Database Insert]
        E --> F[Error Handling]
        F --> G[Statistics Update]
    end
    
    subgraph "Optimization"
        H[Memory Pooling] --> B
        I[Connection Pooling] --> E
        J[Prepared Statements] --> D
        K[Transaction Management] --> E
    end
```

#### Incremental Loading Pattern

```mermaid
graph LR
    subgraph "Incremental Loading"
        A[Source Data] --> B[Change Detection]
        B --> C[Delta Calculation]
        C --> D[Conflict Resolution]
        D --> E[Incremental Insert]
        E --> F[Watermark Update]
    end
    
    subgraph "Change Tracking"
        G[Timestamp Tracking] --> B
        H[Hash Comparison] --> B
        I[Version Control] --> C
        J[Audit Trail] --> F
    end
```

### API Usage Examples

#### Basic Database Loading

```cpp
// Create database loader configuration
LoaderConfig config;
config.loader_type = "postgresql";
config.target_table = "person";
config.parameters["host"] = "localhost";
config.parameters["port"] = 5432;
config.parameters["database"] = "omop_cdm";
config.parameters["username"] = "user";
config.parameters["password"] = "password";
config.batch_config.size = 1000;
config.batch_config.timeout = 30;

// Create and initialize loader
auto loader = LoaderFactory::create_loader("postgresql", config);
loader->initialize(config, context);

// Load data in batches
while (has_more_data()) {
    auto batch = get_next_batch(1000);
    
    // Load batch
    loader->load_batch(batch, context);
    
    // Update progress
    context.update_progress(batch.size());
}

// Get loading statistics
auto stats = loader->get_statistics();
std::cout << "Loaded " << stats.total_records << " records" << std::endl;
std::cout << "Errors: " << stats.error_records << std::endl;
```

#### Batch Processing with Validation

```cpp
// Create batch processor
BatchProcessor processor;
processor.set_batch_size(5000);

// Create batch validator
BatchValidator validator;
validator.add_rule("person_id", "required");
validator.add_rule("birth_date", "date_format");
validator.add_rule("gender", "enum", {"M", "F", "U"});

// Process records
for (const auto& record : records) {
    processor.add_record(record);
    
    if (processor.get_batch_size() >= 5000) {
        // Validate batch
        auto batch = processor.get_current_batch();
        auto validation_result = validator.validate_batch(batch);
        
        if (validation_result.is_valid) {
            // Load batch
            loader->load_batch(batch, context);
        } else {
            // Handle validation errors
            for (const auto& error : validation_result.errors) {
                logger->log(LogLevel::ERROR, "Validation error: " + error);
            }
        }
        
        processor.flush_batch();
    }
}

// Flush remaining records
processor.flush_batch();
```

#### Incremental Loading with Change Detection

```cpp
// Create incremental loader configuration
LoaderConfig config;
config.loader_type = "incremental_postgresql";
config.target_table = "condition_occurrence";
config.parameters["change_column"] = "modified_date";
config.parameters["watermark_table"] = "etl_watermarks";
config.parameters["conflict_resolution"] = "latest_wins";

// Create and initialize incremental loader
auto loader = LoaderFactory::create_loader("incremental_postgresql", config);
loader->initialize(config, context);

// Load incremental data
auto incremental_batch = get_incremental_data(last_watermark);
loader->load_batch(incremental_batch, context);

// Update watermark
update_watermark("condition_occurrence", current_timestamp);
```

### Performance Characteristics

#### Loading Performance Metrics

```mermaid
graph TB
    subgraph "Performance Metrics"
        A[Throughput] --> B[Records/Second]
        A --> C[Bytes/Second]
        A --> D[Batches/Second]
        
        E[Latency] --> F[Connection Time]
        E --> G[Insert Time]
        E --> H[Validation Time]
        
        I[Resource Usage] --> J[Memory Usage]
        I --> K[CPU Usage]
        I --> L[Database Connections]
        
        M[Scalability] --> N[Parallel Loading]
        M --> O[Batch Scaling]
        M --> P[Connection Scaling]
    end
    
    subgraph "Performance Benchmarks"
        Q[Small Batch<br/>1K Records] --> R[~2000 rec/s]
        S[Medium Batch<br/>10K Records] --> T[~8000 rec/s]
        U[Large Batch<br/>100K Records] --> V[~15000 rec/s]
        W[Incremental<br/>1K Records] --> X[~1000 rec/s]
    end
```

#### Performance Benchmarks

| Loading Type | Batch Size | Records | Processing Time | Throughput | Memory Usage |
|--------------|------------|---------|-----------------|------------|--------------|
| Small Batch | 1,000      | 1,000   | 0.5s           | 2,000 rec/s | 100MB       |
| Medium Batch | 10,000     | 10,000  | 1.25s          | 8,000 rec/s | 200MB       |
| Large Batch | 100,000    | 100,000 | 6.67s          | 15,000 rec/s| 500MB       |
| Incremental | 1,000      | 1,000   | 1.0s           | 1,000 rec/s | 150MB       |
| Parallel (4 threads) | 25,000 each | 100,000 | 2.5s | 40,000 rec/s | 1GB         |

#### Optimization Strategies

```mermaid
graph LR
    subgraph "Optimization Techniques"
        A[Batch Processing] --> B[Memory Optimization]
        B --> C[Connection Pooling]
        C --> D[Prepared Statements]
        D --> E[Parallel Processing]
    end
    
    subgraph "Performance Improvements"
        F[10x Faster] --> A
        G[50% Less Memory] --> B
        H[90% Connection Reuse] --> C
        I[4x Parallel] --> D
    end
```

### Error Handling and Recovery

#### Error Handling Patterns

```mermaid
graph TD
    A[Loading Start] --> B{Connection Available}
    B -->|Yes| C[Prepare Batch]
    B -->|No| D[Connection Error]
    
    C --> E{Validation Pass}
    E -->|Yes| F[Execute Insert]
    E -->|No| G[Validation Error]
    
    F --> H{Insert Success}
    H -->|Yes| I[Update Statistics]
    H -->|No| J[Insert Error]
    
    D --> K[Retry Logic]
    G --> L[Data Correction]
    J --> M[Error Recovery]
    
    K --> B
    L --> C
    M --> F
```

#### Recovery Strategies

1. **Connection Recovery**: Automatic reconnection with exponential backoff
2. **Batch Recovery**: Retry failed batches with smaller sizes
3. **Transaction Recovery**: Rollback and retry failed transactions
4. **Data Recovery**: Skip invalid records and continue processing

### Security and Compliance

#### Data Security

```mermaid
graph TB
    subgraph "Security Measures"
        A[Data Encryption] --> B[At Rest]
        A --> C[In Transit]
        A --> D[In Memory]
        
        E[Access Control] --> F[Database Authentication]
        E --> G[Row-Level Security]
        E --> H[Column-Level Security]
        
        I[Data Protection] --> J[Data Masking]
        I --> K[Data Anonymization]
        I --> L[Audit Logging]
    end
```

#### Compliance Features

- **HIPAA Compliance**: Secure handling of PHI during loading
- **GDPR Compliance**: Data retention and deletion capabilities
- **Audit Trail**: Complete logging of all loading operations
- **Data Lineage**: Tracking of data sources and loading history

### Monitoring and Observability

#### Loading Metrics

```cpp
// Enable loading metrics
auto metrics = MetricsCollector::instance();
metrics->register_counter("records_loaded");
metrics->register_counter("loading_errors");
metrics->register_gauge("loading_rate");
metrics->register_histogram("loading_latency");
metrics->register_gauge("batch_size");

// Monitor loading progress
class LoadingMonitor {
public:
    void on_record_loaded() {
        metrics->increment_counter("records_loaded");
    }
    
    void on_loading_error(const std::string& error) {
        metrics->increment_counter("loading_errors");
        logger->log(LogLevel::ERROR, "Loading error: " + error);
    }
    
    void on_batch_complete(size_t batch_size, std::chrono::milliseconds duration) {
        metrics->record_histogram("loading_latency", duration.count());
        metrics->set_gauge("loading_rate", batch_size / (duration.count() / 1000.0));
        metrics->set_gauge("batch_size", batch_size);
    }
    
    void on_transaction_commit() {
        metrics->increment_counter("transactions_committed");
    }
    
    void on_transaction_rollback() {
        metrics->increment_counter("transactions_rolled_back");
    }
};
```

#### Health Checks

```cpp
// Health check implementation
class LoadingHealthCheck {
public:
    HealthStatus check_health() {
        HealthStatus status;
        
        // Check database connectivity
        if (!check_database_connectivity()) {
            status.add_issue("Database connectivity failed");
        }
        
        // Check connection pool health
        if (!check_connection_pool_health()) {
            status.add_issue("Connection pool unhealthy");
        }
        
        // Check loading performance
        if (get_loading_rate() < min_loading_rate_) {
            status.add_issue("Low loading rate");
        }
        
        // Check error rate
        if (get_error_rate() > max_error_rate_) {
            status.add_issue("High error rate");
        }
        
        return status;
    }
};
```

---

## Deployment and Operations

### Build System

The Load Library uses CMake for build management:

```cmake
# src/lib/load/CMakeLists.txt
add_library(omop_load
    batch_loader.cpp
    database_loader.cpp
    loader_strategies.cpp
    additional_loaders.cpp
    loader_base.cpp
    batch_inserter.cpp
)

target_link_libraries(omop_load
    omop_common
    pq
    mysqlclient
    odbc
    spdlog
)
```

### Dependencies

#### Core Dependencies
- **libpq**: PostgreSQL client library
- **mysqlclient**: MySQL client library
- **unixODBC**: ODBC driver manager
- **spdlog**: Logging framework

#### Optional Dependencies
- **libcurl**: HTTP-based data loading
- **zlib**: Compressed data support
- **openssl**: Enhanced security features

### Configuration Management

#### Loading Configuration
```yaml
loading:
  person_table:
    strategy: "batch"
    batch_size: 1000
    parallel_workers: 4
    enable_cdc: false
    tracking_columns:
      - "person_id"
      - "updated_at"
    
  condition_occurrence_table:
    strategy: "incremental"
    batch_size: 500
    watermark_column: "condition_start_date"
    watermark_type: "date"
    auto_update: true
    enable_cdc: true
    cdc_config:
      change_table: "condition_occurrence_changes"
      change_type_column: "change_type"
      change_columns:
        - "condition_occurrence_id"
        - "person_id"
        - "condition_start_date"
      track_deletes: true
      track_updates: true
```

### Operational Procedures

#### Loading Deployment
1. **Configuration Validation**: Validate loading configurations
2. **Database Preparation**: Prepare target databases and tables
3. **Performance Testing**: Test loading performance with sample data
4. **Deployment**: Deploy loading configurations
5. **Monitoring Setup**: Configure monitoring and alerting

#### Quality Assurance
1. **Data Validation**: Validate data before and after loading
2. **Performance Monitoring**: Monitor loading performance metrics
3. **Error Tracking**: Track and categorize loading errors
4. **Audit Trail**: Verify audit trail completeness
5. **Continuous Improvement**: Iterate on loading strategies based on results

---

## Patterns and Anti-Patterns

### Design Patterns Implemented

#### 1. Strategy Pattern
**Location**: `src/lib/load/loader_strategies.h/cpp` (Different loading strategies)
**Implementation**: Multiple loading strategies (batch, incremental, CDC, merge)
**Benefits**:
- Runtime selection of loading strategy
- Easy addition of new loading strategies
- Clean separation of loading logic
- Testable loading components

**Code Example**:
```cpp
class LoadingStrategy {
public:
    virtual ~LoadingStrategy() = default;
    virtual size_t load_batch(const RecordBatch& batch, ProcessingContext& context) = 0;
    virtual bool validate_config(const LoadingConfiguration& config) const = 0;
    virtual std::unordered_map<std::string, std::any> get_statistics() const = 0;
};

class BatchLoadingStrategy : public LoadingStrategy {
public:
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override;
    bool validate_config(const LoadingConfiguration& config) const override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
};

class IncrementalLoadingStrategy : public LoadingStrategy {
public:
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) override;
    bool validate_config(const LoadingConfiguration& config) const override;
    std::unordered_map<std::string, std::any> get_statistics() const override;
    
private:
    std::string watermark_column_;
    std::string current_watermark_;
};
```

#### 2. Factory Pattern
**Location**: `src/lib/load/loader_strategies.h` (Loader creation)
**Implementation**: Factory for creating different loader types
**Benefits**:
- Encapsulates loader creation logic
- Supports runtime loader selection
- Enables plugin architecture for custom loaders
- Facilitates testing with mock loaders

#### 3. Object Pool Pattern
**Location**: `src/lib/load/database_loader.cpp` (Connection pooling)
**Implementation**: Database connection pooling
**Benefits**:
- Efficient connection reuse
- Reduced connection overhead
- Controlled resource usage
- Improved performance for database operations

#### 4. Template Method Pattern
**Location**: `src/lib/load/loader_base.cpp` (Loading workflow)
**Implementation**: Standardized loading workflow
**Benefits**:
- Consistent loading execution across different strategies
- Customizable loading steps through virtual methods
- Common error handling and logging
- Reduces code duplication

#### 5. Observer Pattern
**Location**: `src/lib/load/batch_loader.h` (Progress monitoring)
**Implementation**: Progress callbacks and monitoring
**Benefits**:
- Real-time loading progress monitoring
- Loose coupling between loading and monitoring
- Multiple observer support
- Flexible notification mechanisms

### Anti-Patterns Identified

#### 1. Large Class Anti-Pattern
**Location**: `src/lib/load/database_loader.cpp` (57KB, 1556 lines)
**Issue**: The DatabaseLoader class has grown to handle too many responsibilities
**Problems**:
- Violates Single Responsibility Principle
- Difficult to test and maintain
- High coupling between different concerns
- Complex state management

**Improvement Strategy**:
```cpp
// Split into focused classes
class DatabaseConnectionManager {
    // Handle database connections and pooling
};

class SQLGenerator {
    // Handle SQL generation and optimization
};

class TransactionManager {
    // Handle transaction management
};

class DataValidator {
    // Handle data validation
};

class DatabaseLoader {
private:
    std::unique_ptr<DatabaseConnectionManager> connection_manager_;
    std::unique_ptr<SQLGenerator> sql_generator_;
    std::unique_ptr<TransactionManager> transaction_manager_;
    std::unique_ptr<DataValidator> data_validator_;
    
public:
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context) {
        // Validate data
        auto validated_batch = data_validator_->validate_batch(batch);
        
        // Generate SQL
        auto sql = sql_generator_->generate_insert_sql(validated_batch);
        
        // Execute in transaction
        return transaction_manager_->execute_in_transaction([&]() {
            return connection_manager_->execute_batch(sql, validated_batch);
        });
    }
};
```

#### 2. Complex Configuration Anti-Pattern
**Location**: `src/lib/load/loader_strategies.h` (Configuration handling)
**Issue**: Complex configuration validation and processing
**Problems**:
- Difficult configuration validation
- Inconsistent configuration formats
- Poor error messages for configuration issues
- Complex configuration inheritance

**Improvement Strategy**:
```cpp
// Simplified configuration with validation schemas
class LoadingConfiguration {
public:
    class Builder {
    public:
        Builder& set_strategy(LoadingStrategy strategy);
        Builder& set_batch_size(size_t size);
        Builder& set_parallel_workers(size_t workers);
        Builder& set_table_name(const std::string& table);
        Builder& enable_cdc(bool enable);
        Builder& set_cdc_config(const CDCConfig& config);
        LoadingConfiguration build() const;
        
    private:
        void validate_configuration() const;
    };
    
    // Immutable configuration
    LoadingStrategy get_strategy() const { return strategy_; }
    size_t get_batch_size() const { return batch_size_; }
    size_t get_parallel_workers() const { return parallel_workers_; }
    const std::string& get_table_name() const { return table_name_; }
    bool is_cdc_enabled() const { return cdc_enabled_; }
    const CDCConfig& get_cdc_config() const { return cdc_config_; }
    
private:
    LoadingStrategy strategy_;
    size_t batch_size_;
    size_t parallel_workers_;
    std::string table_name_;
    bool cdc_enabled_;
    CDCConfig cdc_config_;
};

// Configuration validation with clear error messages
class ConfigurationValidator {
public:
    struct ValidationResult {
        bool is_valid;
        std::vector<std::string> errors;
        std::vector<std::string> warnings;
    };
    
    static ValidationResult validate_loading_config(const LoadingConfiguration& config);
    static ValidationResult validate_cdc_config(const CDCConfig& config);
};
```

#### 3. Connection Management Issues
**Location**: `src/lib/load/database_loader.cpp` (Connection handling)
**Issue**: Complex connection lifecycle management with potential resource leaks
**Problems**:
- Manual connection cleanup
- Complex thread synchronization
- Potential connection leaks
- Difficult error recovery

**Improvement Strategy**:
```cpp
// RAII-based connection management
class DatabaseConnection {
public:
    explicit DatabaseConnection(const ConnectionConfig& config);
    ~DatabaseConnection();
    
    // RAII ensures automatic cleanup
    DatabaseConnection(const DatabaseConnection&) = delete;
    DatabaseConnection& operator=(const DatabaseConnection&) = delete;
    
    DatabaseConnection(DatabaseConnection&&) noexcept = default;
    DatabaseConnection& operator=(DatabaseConnection&&) noexcept = default;
    
    bool execute_batch(const std::string& sql, const RecordBatch& batch);
    bool is_valid() const;
    
private:
    class Impl;
    std::unique_ptr<Impl> pimpl_;
};

class ConnectionPool {
private:
    moodycamel::ConcurrentQueue<std::unique_ptr<DatabaseConnection>> pool_;
    std::atomic<size_t> created_connections_{0};
    std::atomic<size_t> active_connections_{0};
    
public:
    std::unique_ptr<DatabaseConnection> acquire() {
        std::unique_ptr<DatabaseConnection> conn;
        if (pool_.try_dequeue(conn)) {
            active_connections_.fetch_add(1);
            return conn;
        }
        
        // Create new connection if pool is empty
        conn = std::make_unique<DatabaseConnection>(config_);
        created_connections_.fetch_add(1);
        active_connections_.fetch_add(1);
        return conn;
    }
    
    void release(std::unique_ptr<DatabaseConnection> conn) {
        if (conn && conn->is_valid()) {
            pool_.enqueue(std::move(conn));
        }
        active_connections_.fetch_sub(1);
    }
};
```

#### 4. Error Handling Inconsistency
**Location**: Throughout load library
**Issue**: Mixed error handling strategies across different loaders
**Problems**:
- Inconsistent error reporting
- Poor error context preservation
- Difficult error recovery
- Complex error propagation

**Improvement Strategy**:
```cpp
// Centralized error handling with consistent context
class LoadingException : public std::runtime_error {
public:
    enum class ErrorCode {
        CONNECTION_ERROR,
        VALIDATION_ERROR,
        TRANSACTION_ERROR,
        CONFLICT_ERROR
    };
    
    struct ErrorContext {
        std::string loader_type;
        std::string table_name;
        std::string operation;
        size_t record_number;
        std::chrono::system_clock::time_point timestamp;
    };
    
    LoadingException(ErrorCode code, const std::string& message, 
                    const ErrorContext& context = {});
    
    ErrorCode get_error_code() const;
    const ErrorContext& get_context() const;
    
private:
    ErrorCode code_;
    ErrorContext context_;
};

// Consistent error handling across loaders
class LoaderBase {
protected:
    void handle_error(const std::string& operation, const std::exception& e, 
                     ProcessingContext& context) {
        LoadingException::ErrorContext error_context{
            get_loader_type(),
            get_table_name(),
            operation,
            context.processed_count(),
            std::chrono::system_clock::now()
        };
        
        throw LoadingException(
            classify_error(e),
            e.what(),
            error_context
        );
    }
};
```

#### 5. Performance Issues
**Location**: `src/lib/load/batch_loader.cpp` (Batch processing)
**Issue**: Inefficient batch processing with suboptimal memory usage
**Problems**:
- Frequent memory allocations
- Suboptimal batch sizes
- Poor memory locality
- Inefficient database operations

**Improvement Strategy**:
```cpp
// Optimized batch processing with memory pooling
class OptimizedBatchLoader {
private:
    struct BatchBuffer {
        std::vector<Record> records;
        size_t capacity;
        size_t current_size;
        
        BatchBuffer(size_t capacity) : capacity(capacity), current_size(0) {
            records.reserve(capacity);
        }
        
        void add_record(Record record) {
            if (current_size < capacity) {
                records.push_back(std::move(record));
                current_size++;
            }
        }
        
        void clear() {
            records.clear();
            current_size = 0;
        }
        
        bool is_full() const { return current_size >= capacity; }
    };
    
    std::unique_ptr<BatchBuffer> current_batch_;
    std::unique_ptr<DatabaseConnection> connection_;
    size_t batch_size_;
    
public:
    size_t load_batch(const RecordBatch& input_batch, ProcessingContext& context) {
        size_t loaded_count = 0;
        
        for (const auto& record : input_batch) {
            current_batch_->add_record(record);
            
            if (current_batch_->is_full()) {
                loaded_count += flush_current_batch(context);
            }
        }
        
        // Flush remaining records
        if (current_batch_->current_size > 0) {
            loaded_count += flush_current_batch(context);
        }
        
        return loaded_count;
    }
    
private:
    size_t flush_current_batch(ProcessingContext& context) {
        auto batch_size = current_batch_->current_size;
        
        try {
            connection_->execute_batch(generate_sql(), current_batch_->records);
            context.increment_processed(batch_size);
            current_batch_->clear();
            return batch_size;
        } catch (const std::exception& e) {
            handle_batch_error(e, context);
            return 0;
        }
    }
};
```

### Recommended Improvements

#### 1. Modular Architecture
- Split large loader classes into focused components
- Implement clear interfaces between modules
- Use dependency injection for better testability
- Establish clear module boundaries

#### 2. Enhanced Error Handling
- Implement consistent exception hierarchy
- Add error recovery mechanisms
- Improve error context preservation
- Add error reporting and monitoring

#### 3. Performance Optimization
- Implement connection pooling with RAII
- Add batch processing optimization
- Optimize database operations
- Add performance profiling capabilities

#### 4. Configuration Management
- Implement configuration validation schemas
- Add configuration versioning and migration
- Improve configuration documentation
- Add configuration testing capabilities

#### 5. Testing Improvements
- Add comprehensive unit tests for all components
- Implement integration tests for loading workflows
- Add performance benchmarking tests
- Implement chaos engineering tests for resilience

### Code Quality Metrics

Based on the analysis of the load library source code:

- **Cyclomatic Complexity**: High in database_loader.cpp (average 12-18 per function)
- **Lines of Code**: DatabaseLoader class is 1556 lines (should be < 500)
- **Coupling**: High coupling between loader components
- **Cohesion**: Low cohesion in some large classes
- **Test Coverage**: Estimated 70-80% (target: 90%+)

### Migration Strategy

1. **Phase 1**: Refactor large loader classes into smaller, focused components
2. **Phase 2**: Implement consistent error handling and logging
3. **Phase 3**: Optimize performance and memory management
4. **Phase 4**: Enhance testing and documentation
5. **Phase 5**: Implement monitoring and observability improvements

This analysis provides a comprehensive roadmap for improving the load library's maintainability, performance, and reliability while preserving its current functionality and ensuring backward compatibility. 