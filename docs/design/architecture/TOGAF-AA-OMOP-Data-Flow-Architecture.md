# OMOP ETL Data Flow Architecture

## Executive Summary

This document provides a detailed architectural analysis of the data flow within the OMOP ETL system, tracing the journey of healthcare data from source systems through extraction, transformation, validation, and loading into the OMOP Common Data Model (CDM). The architecture follows a layered approach with clear separation of concerns, enabling scalable, maintainable, and reliable data processing.

## Table of Contents

1. [System Overview](#system-overview)
2. [Data Flow Architecture](#data-flow-architecture)
3. [Component Layer Analysis](#component-layer-analysis)
4. [Data Transformation Pipeline](#data-transformation-pipeline)
5. [Validation and Quality Assurance](#validation-and-quality-assurance)
6. [Error Handling and Recovery](#error-handling-and-recovery)
7. [Performance and Scalability](#performance-and-scalability)
8. [Security and Compliance](#security-and-compliance)
9. [Monitoring and Observability](#monitoring-and-observability)
10. [Implementation Patterns](#implementation-patterns)
11. [Configuration Management](#configuration-management)
12. [Recommendations](#recommendations)

---

## System Overview

### Architecture Principles

The OMOP ETL system follows these key architectural principles for data flow:

- **Separation of Concerns**: Clear boundaries between Extract, Transform, and Load operations
- **Pipeline Architecture**: Sequential processing with optional parallel execution
- **Configuration-Driven**: Mapping rules and transformations defined in YAML configuration
- **Validation at Every Stage**: Quality checks integrated throughout the pipeline
- **Error Resilience**: Graceful handling of data quality issues and system failures
- **Observability**: Comprehensive logging, metrics, and tracing capabilities

### High-Level Data Flow

```mermaid
graph TB
    subgraph "Source Systems"
        A1[MySQL Database]
        A2[PostgreSQL Database]
        A3[CSV Files]
        A4[JSON Files]
        A5[ODBC Sources]
    end
    
    subgraph "Extract Layer"
        B1[Database Extractors]
        B2[File Extractors]
        B3[Connection Pooling]
        B4[Batch Extraction]
    end
    
    subgraph "Transform Layer"
        C1[Field Mappings]
        C2[Vocabulary Transformations]
        C3[Date Transformations]
        C4[Anonymization]
        C5[Custom Transformations]
    end
    
    subgraph "Validation Layer"
        D1[Schema Validation]
        D2[Business Rule Validation]
        D3[Data Quality Checks]
        D4[Cross-Reference Validation]
    end
    
    subgraph "Load Layer"
        E1[Batch Loading]
        E2[Database Writers]
        E3[Transaction Management]
        E4[Constraint Handling]
    end
    
    subgraph "OMOP CDM"
        F1[Person Table]
        F2[Visit Occurrence]
        F3[Condition Occurrence]
        F4[Procedure Occurrence]
        F5[Measurement]
        F6[Observation]
        F7[Other CDM Tables]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B2
    A5 --> B1
    
    B1 --> C1
    B2 --> C1
    B3 --> B1
    B4 --> B1
    
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> C5
    
    C5 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    
    D4 --> E1
    E1 --> E2
    E2 --> E3
    E3 --> E4
    
    E4 --> F1
    E4 --> F2
    E4 --> F3
    E4 --> F4
    E4 --> F5
    E4 --> F6
    E4 --> F7
```

---

## Data Flow Architecture

### 1. Source Data Layer

The system supports multiple source data formats and systems:

#### Database Sources
- **MySQL**: Primary clinical database with patient, encounter, and diagnosis data
- **PostgreSQL**: Secondary clinical systems and reference data
- **ODBC**: Legacy systems and third-party integrations

#### File Sources
- **CSV Files**: Batch data imports and exports
- **JSON Files**: API responses and structured data feeds
- **Compressed Files**: Gzip and other compression formats

#### Source Data Characteristics
```yaml
source_data:
  mysql:
    tables:
      - patients (person data)
      - encounters (visit data)
      - diagnoses (condition data)
      - procedures (procedure data)
      - measurements (lab results)
    data_volume: "1M-10M records per table"
    update_frequency: "Daily incremental"
    
  csv_files:
    formats:
      - clinical_notes.csv
      - lab_results.csv
      - medication_orders.csv
    encoding: "UTF-8"
    delimiter: ","
```

### 2. Extract Layer Architecture

The extract layer provides unified access to diverse data sources:

#### Core Components

**Extractor Base Class** (`src/lib/extract/extractor_base.h`)
```cpp
class ExtractorBase {
    virtual RecordBatch extract_batch(size_t batch_size, ProcessingContext& context) = 0;
    virtual bool initialize(const std::unordered_map<std::string, std::any>& config) = 0;
    virtual void finalize() = 0;
};
```

**Database Connectors**
- **MySQL Connector**: Optimized for clinical database queries
- **PostgreSQL Connector**: Handles complex joins and window functions
- **ODBC Connector**: Universal database access

**File Extractors**
- **CSV Extractor**: Handles large files with streaming
- **JSON Extractor**: Processes nested JSON structures

#### Extraction Process Flow

```mermaid
sequenceDiagram
    participant P as Pipeline
    participant E as Extractor
    participant DB as Database
    participant C as Connection Pool
    
    P->>E: initialize(config)
    E->>C: get_connection()
    C->>E: connection
    
    loop Batch Processing
        P->>E: extract_batch(batch_size)
        E->>DB: execute_query(query)
        DB->>E: result_set
        E->>E: convert_to_records()
        E->>P: RecordBatch
    end
    
    P->>E: finalize()
    E->>C: release_connection()
```

#### Configuration-Driven Extraction

```yaml
extraction:
  mysql_patients:
    source_query: |
      SELECT 
        p.patient_id,
        p.birth_date,
        p.gender,
        p.race_code,
        p.ethnicity_code
      FROM patients p
      WHERE p.deleted_at IS NULL
    batch_size: 10000
    fetch_size: 5000
    timeout: 300
```

### 3. Transform Layer Architecture

The transform layer applies business rules and converts source data to OMOP CDM format:

#### Transformation Engine

**Core Transformation Engine** (`src/lib/transform/transformation_engine.h`)
```cpp
class TransformationEngine {
    std::any transform(const std::any& input, ProcessingContext& context);
    void register_transformation(const std::string& type, 
                               std::function<std::unique_ptr<FieldTransformation>()> factory);
    bool apply_filters(const core::Record& record, const YAML::Node& filters) const;
};
```

#### Transformation Types

**1. Direct Mapping**
```yaml
transformations:
  - source_column: patient_id
    target_column: person_id
    type: direct
```

**2. Vocabulary Mapping**
```yaml
transformations:
  - source_column: gender
    target_column: gender_concept_id
    type: vocabulary_mapping
    vocabulary: Gender
    mapping:
      "M": 8507  # Male
      "F": 8532  # Female
      "U": 0     # Unknown
```

**3. Date Transformations**
```yaml
transformations:
  - source_column: birth_date
    target_column: birth_datetime
    type: date_transform
    input_format: "%Y-%m-%d"
    output_format: "%Y-%m-%d %H:%M:%S"
    timezone: "UTC"
```

**4. Anonymization**
```yaml
transformations:
  - source_column: patient_id
    target_column: person_id
    type: anonymization
    method: hash
    salt: "${ANONYMIZATION_SALT}"
```

**5. Conditional Transformations**
```yaml
transformations:
  - source_column: diagnosis_code
    target_column: condition_concept_id
    type: conditional
    conditions:
      - when: "icd_version == '10'"
        then: "vocabulary_mapping_icd10"
      - when: "icd_version == '9'"
        then: "vocabulary_mapping_icd9"
```

#### Transformation Pipeline

```mermaid
graph LR
    A[Source Record] --> B[Field Extraction]
    B --> C[Direct Mapping]
    C --> D[Vocabulary Mapping]
    D --> E[Date Transformation]
    E --> F[Anonymization]
    F --> G[Custom Transformations]
    G --> H[Target Record]
    
    I[Configuration] --> J[Transformation Rules]
    J --> C
    J --> D
    J --> E
    J --> F
    J --> G
```

### 4. Validation Layer Architecture

The validation layer ensures data quality and compliance with OMOP CDM standards:

#### Validation Engine

**Validation Engine** (`src/lib/transform/validation_engine.h`)
```cpp
class ValidationEngine {
    omop::common::ValidationResult validate_record(const core::Record& record, 
                                                  ProcessingContext& context);
    omop::common::ValidationResult validate_field(const std::string& field_name,
                                                 const std::any& value,
                                                 ProcessingContext& context);
    void register_validator(const std::string& type, 
                           std::function<std::unique_ptr<IFieldValidator>()> factory);
};
```

#### Validation Types

**1. Required Field Validation**
```yaml
validation_rules:
  - field: person_id
    type: required
    error_message: "Person ID is required"
```

**2. Data Type Validation**
```yaml
validation_rules:
  - field: birth_datetime
    type: date_format
    format: "%Y-%m-%d %H:%M:%S"
    allow_null: false
```

**3. Range Validation**
```yaml
validation_rules:
  - field: age
    type: range
    min: 0
    max: 150
    error_message: "Age must be between 0 and 150"
```

**4. Cross-Reference Validation**
```yaml
validation_rules:
  - field: visit_occurrence_id
    type: foreign_key
    reference_table: visit_occurrence
    reference_field: visit_occurrence_id
```

**5. Business Rule Validation**
```yaml
validation_rules:
  - field: visit_end_date
    type: business_rule
    rule: "visit_end_date >= visit_start_date"
    error_message: "Visit end date must be after start date"
```

#### Validation Process

```mermaid
graph TD
    A[Transformed Record] --> B[Schema Validation]
    B --> C{Schema Valid?}
    C -->|No| D[Schema Errors]
    C -->|Yes| E[Field Validation]
    E --> F{Fields Valid?}
    F -->|No| G[Field Errors]
    F -->|Yes| H[Business Rule Validation]
    H --> I{Business Rules Valid?}
    I -->|No| J[Business Rule Errors]
    I -->|Yes| K[Cross-Reference Validation]
    K --> L{References Valid?}
    L -->|No| M[Reference Errors]
    L -->|Yes| N[Valid Record]
```

### 5. Load Layer Architecture

The load layer handles efficient insertion of validated data into OMOP CDM tables:

#### Loader Components

**Database Loader** (`src/lib/load/database_loader.h`)
```cpp
class DatabaseLoader {
    bool load(const Record& record, ProcessingContext& context);
    size_t load_batch(const RecordBatch& batch, ProcessingContext& context);
    void commit(ProcessingContext& context);
    void rollback(ProcessingContext& context);
};
```

**Batch Inserter** (`src/lib/load/batch_inserter.h`)
```cpp
class BatchInserter {
    void add_record(const Record& record);
    size_t flush(ProcessingContext& context);
    void set_batch_size(size_t size);
};
```

#### Loading Strategies

**1. Batch Insertion**
```yaml
loading:
  strategy: batch_insert
  batch_size: 10000
  commit_interval: 50000
  retry_attempts: 3
  timeout: 300
```

**2. Upsert Operations**
```yaml
loading:
  strategy: upsert
  conflict_resolution: update
  conflict_fields: [person_id, visit_start_date]
```

**3. Transaction Management**
```yaml
loading:
  transaction_mode: auto_commit
  isolation_level: read_committed
  rollback_on_error: true
```

#### Loading Process Flow

```mermaid
sequenceDiagram
    participant V as Validator
    participant L as Loader
    participant BI as Batch Inserter
    participant DB as Database
    participant T as Transaction
    
    V->>L: validated_record
    L->>BI: add_record(record)
    
    alt Batch Full
        BI->>T: begin_transaction()
        BI->>DB: batch_insert(records)
        DB->>BI: insert_result
        BI->>T: commit()
        T->>BI: commit_success
    end
    
    alt Error Occurs
        BI->>T: rollback()
        T->>BI: rollback_success
        BI->>L: error_notification
    end
```

---

## Component Layer Analysis

### Core Pipeline Architecture

The core pipeline orchestrates the entire data flow:

**ETL Pipeline** (`src/lib/core/pipeline.h`)
```cpp
class ETLPipeline {
    bool initialize(const ETLPipelineConfig& config);
    std::string start();
    bool stop(bool force = false);
    PipelineStatus wait_for_completion(std::chrono::seconds timeout);
    PipelineExecutionStats get_execution_stats() const;
};
```

#### Pipeline Stages

```mermaid
graph TB
    subgraph "Pipeline Execution"
        A[Initialize] --> B[Extract Stage]
        B --> C[Transform Stage]
        C --> D[Validate Stage]
        D --> E[Load Stage]
        E --> F[Finalize]
    end
    
    subgraph "Parallel Processing"
        G[Extract Worker] --> H[Extract Queue]
        H --> I[Transform Worker]
        I --> J[Transform Queue]
        J --> K[Load Worker]
    end
    
    subgraph "Monitoring"
        L[Metrics Collector]
        M[Progress Callbacks]
        N[Error Handlers]
    end
```

### Service Layer Architecture

The service layer provides high-level ETL operations:

**ETL Service** (`src/lib/service/etl_service.h`)
```cpp
class ETLService {
    std::string create_job(const ETLJobRequest& request);
    std::optional<ETLJobResult> get_job_result(const std::string& job_id);
    bool cancel_job(const std::string& job_id);
    std::unordered_map<std::string, std::string> run_all_tables(bool parallel = false);
};
```

#### Service Orchestration

```mermaid
graph LR
    A[API Request] --> B[ETL Service]
    B --> C[Job Manager]
    C --> D[Pipeline Manager]
    D --> E[ETL Pipeline]
    E --> F[Extract Service]
    E --> G[Transform Service]
    E --> H[Load Service]
    
    F --> I[Source Systems]
    G --> J[Transformation Engine]
    H --> K[OMOP CDM]
```

---

## Data Transformation Pipeline

### Configuration-Driven Mapping

The system uses YAML configuration files to define data mappings:

**MySQL to OMOP Mapping** (`config/etl/mysql_mappings.yaml`)
```yaml
mappings:
  person:
    source_query: |
      SELECT
        p.patient_id,
        p.birth_date,
        p.gender,
        p.race_code,
        p.ethnicity_code
      FROM patients p
      WHERE p.deleted_at IS NULL
    target_table: person
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
    validation_rules:
      - field: person_id
        type: required
```

### Transformation Rules Engine

The transformation engine applies rules in sequence:

```mermaid
graph TD
    A[Source Field] --> B[Direct Mapping]
    B --> C[Type Conversion]
    C --> D[Vocabulary Mapping]
    D --> E[Date Transformation]
    E --> F[Anonymization]
    F --> G[Custom Logic]
    G --> H[Target Field]
    
    I[Configuration] --> J[Rule Engine]
    J --> B
    J --> C
    J --> D
    J --> E
    J --> F
    J --> G
```

### Vocabulary Service Integration

The vocabulary service provides concept mapping:

**Vocabulary Service** (`src/lib/transform/vocabulary_service.h`)
```cpp
class VocabularyService {
    int32_t map_concept(const std::string& source_value, 
                       const std::string& source_vocabulary,
                       const std::string& target_vocabulary);
    std::vector<Concept> search_concepts(const std::string& query);
    bool validate_concept_id(int32_t concept_id);
};
```

---

## Validation and Quality Assurance

### Multi-Level Validation

The system implements validation at multiple levels:

**1. Schema Validation**
- Field presence and data types
- Constraint checking
- Format validation

**2. Business Rule Validation**
- Domain-specific rules
- Cross-field validation
- Temporal consistency

**3. Data Quality Validation**
- Completeness checks
- Accuracy validation
- Consistency verification

### Quality Metrics

```yaml
quality_metrics:
  completeness:
    required_fields: 100%
    optional_fields: >95%
  accuracy:
    valid_concept_ids: >99%
    valid_date_formats: >99.5%
  consistency:
    referential_integrity: 100%
    temporal_consistency: >99%
```

### Error Handling

```mermaid
graph TD
    A[Validation Error] --> B{Error Type}
    B -->|Schema Error| C[Log Error]
    B -->|Business Rule Error| D[Apply Correction]
    B -->|Quality Error| E[Flag for Review]
    
    C --> F[Skip Record]
    D --> G[Retry Transformation]
    E --> H[Manual Review Queue]
    
    F --> I[Error Report]
    G --> J[Re-validation]
    H --> K[Quality Dashboard]
```

---

## Error Handling and Recovery

### Error Classification

**1. Data Quality Errors**
- Missing required fields
- Invalid data formats
- Out-of-range values

**2. System Errors**
- Database connection failures
- Memory exhaustion
- Network timeouts

**3. Configuration Errors**
- Invalid mapping rules
- Missing vocabulary entries
- Incorrect field references

### Recovery Strategies

**1. Automatic Recovery**
```yaml
error_handling:
  retry_attempts: 3
  backoff_strategy: exponential
  max_retry_delay: 300
  circuit_breaker:
    failure_threshold: 10
    recovery_timeout: 60
```

**2. Checkpoint and Restart**
```yaml
checkpointing:
  enabled: true
  interval: 50000
  location: "/var/omop/checkpoints"
  retention: 7_days
```

**3. Error Reporting**
```yaml
error_reporting:
  log_level: ERROR
  error_file: "/var/omop/logs/errors.log"
  alert_threshold: 100
  notification_email: "<EMAIL>"
```

---

## Performance and Scalability

### Parallel Processing

The system supports multiple parallel processing modes:

**1. Pipeline Parallelism**
```yaml
execution_mode: parallel
parallel_threads: 4
queue_sizes:
  extract: 1000
  transform: 2000
  load: 1000
```

**2. Batch Processing**
```yaml
batch_processing:
  extract_batch_size: 10000
  transform_batch_size: 5000
  load_batch_size: 10000
  memory_limit: 2GB
```

**3. Streaming Processing**
```yaml
streaming:
  enabled: true
  buffer_size: 1000
  flush_interval: 30
```

### Performance Optimization

**1. Database Optimization**
- Connection pooling
- Prepared statements
- Batch operations
- Index optimization

**2. Memory Management**
- Streaming data processing
- Garbage collection optimization
- Memory-mapped files for large datasets

**3. Network Optimization**
- Compression for file transfers
- Connection reuse
- Parallel downloads

---

## Security and Compliance

### Data Protection

**1. Anonymization**
```yaml
anonymization:
  enabled: true
  methods:
    - hash_salt
    - k_anonymity
    - differential_privacy
  fields:
    - patient_id
    - name
    - address
```

**2. Access Control**
```yaml
access_control:
  authentication: ldap
  authorization: role_based
  audit_logging: enabled
  encryption: aes_256
```

**3. Compliance**
- GDPR compliance
- HIPAA compliance
- Data retention policies
- Audit trails

### Security Architecture

```mermaid
graph TB
    A[User Request] --> B[Authentication]
    B --> C[Authorization]
    C --> D[Data Access]
    D --> E[Anonymization]
    E --> F[Encryption]
    F --> G[Audit Log]
    G --> H[Data Processing]
```

---

## Monitoring and Observability

### Metrics Collection

**1. Performance Metrics**
```yaml
metrics:
  throughput:
    records_per_second: true
    bytes_per_second: true
  latency:
    extract_time: true
    transform_time: true
    load_time: true
  quality:
    error_rate: true
    validation_failures: true
```

**2. System Metrics**
- CPU usage
- Memory consumption
- Disk I/O
- Network usage

**3. Business Metrics**
- Records processed
- Data quality scores
- Processing time
- Error rates

### Logging and Tracing

**1. Structured Logging**
```yaml
logging:
  level: INFO
  format: json
  output: file
  rotation:
    max_size: 100MB
    max_files: 10
```

**2. Distributed Tracing**
- Request tracing
- Performance profiling
- Error correlation

**3. Alerting**
```yaml
alerts:
  error_rate_threshold: 0.05
  processing_time_threshold: 300
  memory_usage_threshold: 0.8
  notification_channels:
    - email
    - slack
    - pagerduty
```

---

## Implementation Patterns

### Factory Pattern

Component creation uses the factory pattern:

```cpp
class ComponentFactory {
    template<typename T>
    static std::unique_ptr<T> create(const std::string& type,
                                    const std::unordered_map<std::string, std::any>& config);
};
```

### Strategy Pattern

Different processing strategies can be configured:

```cpp
class ProcessingStrategy {
    virtual void process(RecordBatch& batch, ProcessingContext& context) = 0;
};

class SequentialStrategy : public ProcessingStrategy;
class ParallelStrategy : public ProcessingStrategy;
class StreamingStrategy : public ProcessingStrategy;
```

### Observer Pattern

Progress monitoring and error handling:

```cpp
class PipelineObserver {
    virtual void on_progress(const PipelineExecutionStats& stats) = 0;
    virtual void on_error(const std::string& error, const PipelineExecutionStats& stats) = 0;
    virtual void on_completion(const PipelineExecutionStats& stats) = 0;
};
```

### Configuration Pattern

YAML-based configuration management:

```cpp
class ConfigurationManager {
    YAML::Node load_config(const std::string& path);
    std::any get_value(const std::string& key);
    void set_value(const std::string& key, const std::any& value);
};
```

---

## Configuration Management

### Configuration Structure

**1. Source Configuration**
```yaml
source:
  type: mysql
  connection:
    host: localhost
    port: 3306
    database: clinical_db
    username: ${DB_USER}
    password: ${DB_PASSWORD}
```

**2. Target Configuration**
```yaml
target:
  type: postgresql
  connection:
    host: localhost
    port: 5432
    database: omop_cdm
    username: ${OMOP_USER}
    password: ${OMOP_PASSWORD}
```

**3. Pipeline Configuration**
```yaml
pipeline:
  batch_size: 10000
  parallel_threads: 4
  error_tolerance: 0.01
  checkpoint_interval: 50000
```

### Environment-Specific Configuration

**Development Environment**
```yaml
environment: development
logging:
  level: DEBUG
performance:
  batch_size: 1000
  parallel_threads: 2
```

**Production Environment**
```yaml
environment: production
logging:
  level: INFO
performance:
  batch_size: 10000
  parallel_threads: 8
```

---

## Recommendations

### Immediate Improvements

1. **Enhanced Error Handling**
   - Implement circuit breaker pattern for external dependencies
   - Add retry mechanisms with exponential backoff
   - Improve error categorization and reporting

2. **Performance Optimization**
   - Implement streaming processing for large datasets
   - Add connection pooling for database operations
   - Optimize memory usage with object pooling

3. **Monitoring Enhancement**
   - Add real-time dashboards for pipeline monitoring
   - Implement alerting for critical failures
   - Add performance profiling capabilities

### Long-term Enhancements

1. **Scalability Improvements**
   - Implement distributed processing with message queues
   - Add horizontal scaling capabilities
   - Support for cloud-native deployment

2. **Data Quality Enhancement**
   - Implement machine learning-based data quality detection
   - Add automated data profiling
   - Enhance validation rule engine

3. **Security Strengthening**
   - Implement end-to-end encryption
   - Add role-based access control
   - Enhance audit logging capabilities

### Architecture Evolution

1. **Microservices Migration**
   - Split monolithic ETL into microservices
   - Implement service mesh for communication
   - Add API gateway for external access

2. **Event-Driven Architecture**
   - Implement event sourcing for data lineage
   - Add real-time processing capabilities
   - Support for streaming analytics

3. **Cloud-Native Deployment**
   - Containerize all components
   - Implement Kubernetes orchestration
   - Add auto-scaling capabilities

---

## Conclusion

The OMOP ETL system implements a robust, scalable, and maintainable data flow architecture that effectively transforms healthcare data from various source systems into the standardized OMOP CDM format. The layered approach with clear separation of concerns, comprehensive validation, and configurable transformation rules provides a solid foundation for healthcare data integration.

The system's ability to handle multiple data sources, apply complex transformations, and ensure data quality makes it suitable for production healthcare environments. The modular design allows for easy extension and customization to meet specific organizational requirements.

Future enhancements should focus on improving scalability, enhancing monitoring capabilities, and strengthening security measures to support the growing demands of healthcare data processing. 