source_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test
  username: test
  password: test
target_database:
  type: postgresql
  host: localhost
  port: 5432
  database: test
  username: test
  password: test
table_mappings:
  person:
    source_table: patient_demographics
    target_table: person
    field_mappings:
      patient_id: person_id
      birth_date: birth_datetime
      gender: gender_concept_id
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
      - source_column: birth_date
        target_column: birth_datetime
        type: direct
      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        default_value: 0
vocabulary_mappings:
  Gender:
    M: 8507
    F: 8532