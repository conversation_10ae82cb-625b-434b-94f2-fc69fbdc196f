# tests/unit/transform/CMakeLists.txt - Standardized Unit Tests for Transform Library

# Test source files (alphabetically sorted)
set(TRANSFORM_TEST_SOURCES
    alphabetical_date_transformation_unit_test.cpp
    anonymization_transformations_unit_test.cpp
    comprehensive_transformation_suite_unit_test.cpp
    conditional_transformation_logic_unit_test.cpp
    custom_transformation_rules_unit_test.cpp
    date_transformation_operations_unit_test.cpp
    field_transformation_helpers_unit_test.cpp
    field_mapping_transformation_unit_test.cpp
    numeric_transformation_operations_unit_test.cpp
    string_transformation_operations_unit_test.cpp
    transformation_integration_unit_test.cpp
    transformation_utilities_unit_test.cpp
    transformation_edge_case_handling_unit_test.cpp
    transformation_edge_cases_comprehensive_unit_test.cpp
    transformation_engine_execution_unit_test.cpp
    transformation_memory_management_unit_test.cpp
    transformation_pipeline_comprehensive_unit_test.cpp
    transformation_registry_management_unit_test.cpp
    uk_healthcare_transformation_comprehensive_unit_test.cpp
    validation_engine_unit_test.cpp
    vocabulary_mapping_service_unit_test.cpp
    vocabulary_service_advanced_features_unit_test.cpp
    vocabulary_transformation_operations_unit_test.cpp
)

# Use the consolidated approach from parent CMakeLists.txt
add_component_unit_tests(transform ${TRANSFORM_TEST_SOURCES})

# Add additional dependencies for transform tests
target_link_libraries(test_omop_transform_unit PRIVATE
    yaml-cpp::yaml-cpp
    nlohmann_json::nlohmann_json
    fmt::fmt
    OpenSSL::Crypto
    OpenSSL::SSL
)

# Add UNIT_TESTING flag for test-only methods
target_compile_definitions(test_omop_transform_unit PRIVATE UNIT_TESTING)

# Set longer timeout for transform tests due to complex logic
set_tests_properties(test_omop_transform_unit PROPERTIES
    LABELS "unit;transform"
    TIMEOUT 900  # 15 minutes for complex transformation tests
)

# Enable sanitizers for transform tests if enabled
if(ENABLE_SANITIZERS)
    target_compile_options(test_omop_transform_unit PRIVATE
        -fsanitize=address,undefined
        -fno-omit-frame-pointer
    )
    target_link_options(test_omop_transform_unit PRIVATE
        -fsanitize=address,undefined
    )
endif()