#include <gtest/gtest.h>
#include "transform/vocabulary_service.h"
#include "test_helpers/mock_database.h"
#include <thread>
#include <chrono>

namespace omop::test {

class VocabularyServiceAdvancedTest : public ::testing::Test {
protected:
    void SetUp() override {
        auto mock_connection = std::make_unique<MockDatabaseConnection>();
        mock_connection_ = mock_connection.get();
        setupMockData();
        
        vocabulary_service_ = std::make_unique<transform::VocabularyService>(
            std::move(mock_connection));
    }

    void setupMockData() {
        // Setup mock responses for vocabulary tables
        setupConceptTable();
        setupRelationshipTable();
        setupMappingTable();
    }

    void setupConceptTable() {
        // Mock concept data with versioning
        mock_connection_->add_query_result(
            "SELECT concept_id, concept_name, domain_id, vocabulary_id, "
            "concept_class_id, standard_concept, concept_code, "
            "valid_start_date, valid_end_date FROM concept WHERE concept_id = $1",
            {
                {8507, "MALE", "Gender", "Gender", "Gender", "S", "M", 
                 "1970-01-01", "2099-12-31"}
            }
        );
    }

    void setupRelationshipTable() {
        // Mock relationship data
        mock_connection_->add_query_result(
            "SELECT concept_id_1, concept_id_2, relationship_id FROM concept_relationship "
            "WHERE concept_id_1 = $1 AND relationship_id = 'Is a'",
            {
                {320128, 316139, "Is a"} // Hypertension is a cardiovascular disease
            }
        );
    }

    void setupMappingTable() {
        // Mock mapping data
        mock_connection_->add_query_result(
            "SELECT source_value, source_vocabulary, target_concept_id, "
            "target_vocabulary, mapping_type, confidence FROM vocabulary_mappings "
            "WHERE source_value = $1",
            {
                {"HTN", "Internal", 320128, "SNOMED", "custom", 1.0f}
            }
        );
    }

    MockDatabaseConnection* mock_connection_;
    std::unique_ptr<transform::VocabularyService> vocabulary_service_;
};

// Test scheduled vocabulary updates
TEST_F(VocabularyServiceAdvancedTest, ScheduledUpdates) {
    // Configure advanced initialization
    YAML::Node config;
    config["cache_size"] = 1000;
    config["scheduled_updates"]["enabled"] = true;
    config["scheduled_updates"]["update_interval_hours"] = 1;
    config["scheduled_updates"]["quiet_hours_start"] = 2;
    config["scheduled_updates"]["quiet_hours_end"] = 6;
    config["scheduled_updates"]["batch_size"] = 10;
    
    vocabulary_service_->initialize_advanced(config);
    
    // Add some unrecognized terms
    vocabulary_service_->record_unrecognised_term("hypertension", "condition", "ICD10");
    vocabulary_service_->record_unrecognised_term("diabetes mellitus", "condition", "ICD10");
    
    // Wait briefly to ensure scheduler has started
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Stop scheduler for test cleanup
    vocabulary_service_->set_scheduled_updates_enabled(false);
}

// Test version management
TEST_F(VocabularyServiceAdvancedTest, VersionManagement) {
    vocabulary_service_->initialize(1000);
    auto& version_manager = vocabulary_service_->get_version_manager();
    
    // Begin a version transaction
    version_manager.begin_transaction("Add custom mappings for conditions");
    
    // Add some mappings
    transform::VocabularyMapping mapping1;
    mapping1.source_value = "HTN";
    mapping1.source_vocabulary = "Internal";
    mapping1.target_concept_id = 320128;
    mapping1.mapping_confidence = 1.0f;
    
    vocabulary_service_->add_mapping(mapping1);
    
    // Commit transaction
    version_manager.commit_transaction();
    
    // Verify version is available
    auto current_version = version_manager.get_current_version();
    EXPECT_FALSE(current_version.empty());
}

// Test conflict resolution
TEST_F(VocabularyServiceAdvancedTest, ConflictResolution) {
    vocabulary_service_->initialize(1000);
    auto& resolver = vocabulary_service_->get_conflict_resolver();
    
    // Create conflicting mappings
    std::vector<transform::VocabularyMapping> candidates;
    
    transform::VocabularyMapping mapping1;
    mapping1.source_value = "BP";
    mapping1.target_concept_id = 4152194; // Blood pressure
    mapping1.target_vocabulary = "SNOMED";
    mapping1.mapping_confidence = 0.9f;
    candidates.push_back(mapping1);
    
    transform::VocabularyMapping mapping2;
    mapping2.source_value = "BP";
    mapping2.target_concept_id = 4044013; // Bipolar disorder
    mapping2.target_vocabulary = "SNOMED";
    mapping2.mapping_confidence = 0.85f;
    candidates.push_back(mapping2);
    
    // Resolve without context
    auto resolution = resolver.resolve_conflict("BP", candidates);
    EXPECT_EQ(resolution.selected_mapping.target_concept_id, 4152194);
    EXPECT_TRUE(resolution.requires_human_review); // Close scores
    
    // Resolve with context
    mapping1.context = "vital_signs";
    candidates[0] = mapping1;
    resolution = resolver.resolve_conflict("BP", candidates, "vital_signs");
    EXPECT_EQ(resolution.selected_mapping.target_concept_id, 4152194);
    EXPECT_FALSE(resolution.requires_human_review); // Clear winner with context
}

// Test batch processing performance
TEST_F(VocabularyServiceAdvancedTest, BatchProcessingPerformance) {
    vocabulary_service_->initialize(10000);
    
    // Create a large batch of terms
    std::vector<std::string> terms;
    for (int i = 0; i < 1000; ++i) {
        terms.push_back("term_" + std::to_string(i));
    }
    
    auto start = std::chrono::high_resolution_clock::now();
    auto results = vocabulary_service_->process_terms_batch(terms);
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // Verify performance - should process 1000 terms in under 5 seconds
    EXPECT_LT(duration.count(), 5000);
    
    // Log performance metrics
    double terms_per_second = 1000.0 / (duration.count() / 1000.0);
    std::cout << "Batch processing performance: " << terms_per_second << " terms/second" << std::endl;
}

// Test external service integration
TEST_F(VocabularyServiceAdvancedTest, ExternalServiceIntegration) {
    // Configure external services
    YAML::Node services;
    services[0]["name"] = "NHS_Terminology_Server";
    services[0]["type"] = "database";
    services[0]["connection"]["host"] = "nhs-terms.example.com";
    services[0]["connection"]["database"] = "terminology";
    services[0]["priority"] = 1;
    
    services[1]["name"] = "UMLS_API";
    services[1]["type"] = "rest_api";
    services[1]["endpoint"] = "https://uts-ws.nlm.nih.gov/rest";
    services[1]["priority"] = 2;
    
    vocabulary_service_->configure_external_services(services);
    
    // Test external updates processing
    size_t updates = vocabulary_service_->process_external_updates();
    EXPECT_GE(updates, 0); // Should not fail
}

// Test abbreviation and acronym handling
TEST_F(VocabularyServiceAdvancedTest, AbbreviationHandling) {
    vocabulary_service_->initialize(1000);
    
    // Test context-dependent abbreviations
    struct AbbreviationTest {
        std::string abbreviation;
        std::string context;
        int expected_concept_id;
        std::string expected_name;
    };
    
    std::vector<AbbreviationTest> tests = {
        {"BP", "vital_signs", 4152194, "Blood pressure"},
        {"BP", "psychiatry", 4044013, "Bipolar disorder"},
        {"MI", "cardiology", 312327, "Myocardial infarction"},
        {"MI", "ophthalmology", 374009, "Myocardial infarction of eye"},
        {"CVA", "neurology", 373503, "Cerebrovascular accident"},
        {"CVA", "ophthalmology", 374009, "Cerebrovascular accident of eye"}
    };
    
    for (const auto& test : tests) {
        // Add mappings for context-dependent resolution
        transform::VocabularyMapping mapping;
        mapping.source_value = test.abbreviation;
        mapping.source_vocabulary = "Internal";
        mapping.target_concept_id = test.expected_concept_id;
        mapping.context = test.context;
        mapping.mapping_confidence = 0.95f;
        
        vocabulary_service_->add_mapping(mapping);
        
        // Test resolution with context
        int concept_id = vocabulary_service_->map_to_concept_id(
            test.abbreviation, "Internal", test.context);
        
        EXPECT_EQ(concept_id, test.expected_concept_id) 
            << "Failed to resolve " << test.abbreviation 
            << " in context " << test.context;
    }
}

// Test parallel processing capabilities
TEST_F(VocabularyServiceAdvancedTest, ParallelProcessing) {
    vocabulary_service_->initialize(10000);
    
    // Create test terms
    std::vector<std::string> terms = {
        "hypertension", "diabetes", "asthma", "depression",
        "hypertension", "diabetes", "asthma", "depression" // Duplicates for testing
    };
    
    // Test batch processing performance
    auto start = std::chrono::high_resolution_clock::now();
    auto results = vocabulary_service_->process_terms_batch(terms);
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    // Verify we get some results (even if not all terms are found)
    EXPECT_LE(results.size(), terms.size());
    
    std::cout << "Batch processing: " << duration.count() << " microseconds" << std::endl;
}

// Test human review workflow
TEST_F(VocabularyServiceAdvancedTest, HumanReviewWorkflow) {
    vocabulary_service_->initialize(1000);
    
    // Create conflicting mappings that require human review
    std::vector<transform::VocabularyMapping> candidates;
    
    transform::VocabularyMapping mapping1;
    mapping1.source_value = "ambiguous_term";
    mapping1.target_concept_id = 1001;
    mapping1.mapping_confidence = 0.75f;
    candidates.push_back(mapping1);
    
    transform::VocabularyMapping mapping2;
    mapping2.source_value = "ambiguous_term";
    mapping2.target_concept_id = 1002;
    mapping2.mapping_confidence = 0.78f;
    candidates.push_back(mapping2);
    
    // This should trigger human review
    auto& resolver = vocabulary_service_->get_conflict_resolver();
    auto resolution = resolver.resolve_conflict("ambiguous_term", candidates);
    
    EXPECT_TRUE(resolution.requires_human_review);
    EXPECT_EQ(resolution.alternatives.size(), 2);
    
    // Simulate human review decision
    vocabulary_service_->apply_review_decision("ambiguous_term", 1001);
    
    // Verify the decision was applied
    auto pending_review = vocabulary_service_->get_terms_pending_review();
    bool found = false;
    for (const auto& [term, _] : pending_review) {
        if (term == "ambiguous_term") {
            found = true;
            break;
        }
    }
    EXPECT_FALSE(found); // Should be removed from pending review
}

// Test ML term identification
TEST_F(VocabularyServiceAdvancedTest, MLTermIdentification) {
    vocabulary_service_->initialize(1000);
    
    // Enable ML term identification
    vocabulary_service_->enable_ml_term_identification("/path/to/model");
    
    // Test medical term identification
    std::vector<std::string> medical_terms = {
        "hypertension", "diabetes_mellitus", "myocardial_infarction",
        "cerebrovascular_accident", "chronic_obstructive_pulmonary_disease"
    };
    
    std::vector<std::string> non_medical_terms = {
        "apple", "car", "house", "computer", "book"
    };
    
    for (const auto& term : medical_terms) {
        EXPECT_TRUE(vocabulary_service_->is_medical_term(term))
            << "Failed to identify medical term: " << term;
    }
    
    for (const auto& term : non_medical_terms) {
        EXPECT_FALSE(vocabulary_service_->is_medical_term(term))
            << "Incorrectly identified as medical term: " << term;
    }
}

// Test UK-specific medical terminology
TEST_F(VocabularyServiceAdvancedTest, UKMedicalTerminology) {
    vocabulary_service_->initialize(1000);
    
    // Test UK-specific medical terms and spellings
    struct UKTermTest {
        std::string uk_term;
        std::string us_term;
        bool should_be_equivalent;
    };
    
    std::vector<UKTermTest> tests = {
        {"anaemia", "anemia", true},
        {"oesophagitis", "esophagitis", true},
        {"haemoglobin", "hemoglobin", true},
        {"paediatric", "pediatric", true},
        {"gynaecology", "gynecology", true},
        {"paracetamol", "acetaminophen", true}, // UK vs US drug names
        {"adrenaline", "epinephrine", true},    // UK vs US drug names
        {"stone", "pound", false},              // Different weight units
        {"centigrade", "fahrenheit", false}     // Different temperature units
    };
    
    for (const auto& test : tests) {
        // Add UK term mapping
        transform::VocabularyMapping uk_mapping;
        uk_mapping.source_value = test.uk_term;
        uk_mapping.source_vocabulary = "UK_Medical";
        uk_mapping.target_concept_id = 1001; // Example concept ID
        uk_mapping.mapping_confidence = 0.9f;
        
        vocabulary_service_->add_mapping(uk_mapping);
        
        // Test UK term recognition
        int uk_concept_id = vocabulary_service_->map_to_concept_id(
            test.uk_term, "UK_Medical");
        
        EXPECT_NE(uk_concept_id, 0) 
            << "Failed to recognize UK term: " << test.uk_term;
    }
}

// Test vocabulary versioning and basic transaction operations
TEST_F(VocabularyServiceAdvancedTest, VersioningAndRollback) {
    vocabulary_service_->initialize(1000);
    auto& version_manager = vocabulary_service_->get_version_manager();
    
    // Create initial version
    version_manager.begin_transaction("Initial vocabulary setup");
    
    transform::VocabularyMapping initial_mapping;
    initial_mapping.source_value = "initial_term";
    initial_mapping.target_concept_id = 1001;
    vocabulary_service_->add_mapping(initial_mapping);
    
    version_manager.commit_transaction();
    
    // Create second version with modification  
    version_manager.begin_transaction("Update vocabulary mappings");
    
    transform::VocabularyMapping updated_mapping;
    updated_mapping.source_value = "initial_term";
    updated_mapping.target_concept_id = 1002; // Changed concept ID
    vocabulary_service_->add_mapping(updated_mapping);
    
    version_manager.commit_transaction();
    
    // Verify version is tracked
    auto current_version = version_manager.get_current_version();
    EXPECT_FALSE(current_version.empty());
    
    // Test rollback capability exists (even if not fully implemented)
    version_manager.begin_transaction("Test rollback");
    version_manager.rollback_transaction(); // Should not crash
}

// Test memory management and cache performance
TEST_F(VocabularyServiceAdvancedTest, MemoryManagement) {
    // Test with large cache size
    vocabulary_service_->initialize(100000);
    
    // Add many mappings to test memory usage
    for (int i = 0; i < 10000; ++i) {
        transform::VocabularyMapping mapping;
        mapping.source_value = "term_" + std::to_string(i);
        mapping.target_concept_id = 1000 + (i % 100);
        mapping.mapping_confidence = 0.8f;
        
        vocabulary_service_->add_mapping(mapping);
    }
    
    // Get cache statistics
    auto stats = vocabulary_service_->get_cache_stats();
    EXPECT_GT(stats.cache_size, 0);
    EXPECT_GT(stats.hit_rate, 0.0);
    
    // Test cache clearing
    vocabulary_service_->clear_cache();
    stats = vocabulary_service_->get_cache_stats();
    EXPECT_EQ(stats.cache_size, 0);
    EXPECT_EQ(stats.hits, 0);
}

// Test error handling and resilience
TEST_F(VocabularyServiceAdvancedTest, ErrorHandling) {
    vocabulary_service_->initialize(1000);
    
    // Test with invalid concept IDs
    int invalid_concept_id = vocabulary_service_->map_to_concept_id(
        "nonexistent_term", "InvalidVocabulary");
    EXPECT_EQ(invalid_concept_id, 0);
    
    // Test with empty or null values
    int empty_result = vocabulary_service_->map_to_concept_id("", "TestVocabulary");
    EXPECT_EQ(empty_result, 0);
    
    // Test with very long terms
    std::string long_term(10000, 'a');
    int long_result = vocabulary_service_->map_to_concept_id(long_term, "TestVocabulary");
    EXPECT_EQ(long_result, 0);
    
    // Test concurrent access (basic thread safety)
    std::vector<std::thread> threads;
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([this, i]() {
            transform::VocabularyMapping mapping;
            mapping.source_value = "concurrent_term_" + std::to_string(i);
            mapping.target_concept_id = 1000 + i;
            vocabulary_service_->add_mapping(mapping);
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Verify no crashes occurred
    EXPECT_TRUE(true);
}

// Test integration with external medical dictionaries
TEST_F(VocabularyServiceAdvancedTest, ExternalDictionaryIntegration) {
    vocabulary_service_->initialize(1000);
    
    // Add mock medical dictionaries
    auto nhs_connection = std::make_unique<MockDatabaseConnection>();
    vocabulary_service_->add_medical_dictionary("NHS_Dictionary", std::move(nhs_connection), 1);
    
    auto snomed_connection = std::make_unique<MockDatabaseConnection>();
    vocabulary_service_->add_medical_dictionary("SNOMED_UK", std::move(snomed_connection), 2);
    
    auto bnf_connection = std::make_unique<MockDatabaseConnection>();
    vocabulary_service_->add_medical_dictionary("BNF", std::move(bnf_connection), 3);
    
    // Enable auto-learn
    vocabulary_service_->set_auto_learn_enabled(true);
    
    // Record unrecognized terms
    vocabulary_service_->record_unrecognised_term("paracetamol", "medication", "Internal");
    vocabulary_service_->record_unrecognised_term("anaemia", "condition", "Internal");
    
    // Process unrecognized terms
    size_t processed = vocabulary_service_->process_unrecognised_terms();
    EXPECT_GE(processed, 0); // Should not fail
    
    // Verify terms were processed
    auto unrecognized_terms = vocabulary_service_->get_unrecognised_terms();
    // Note: In a real test, we'd verify the terms were actually mapped
    // For now, we just ensure the process doesn't crash
}

} // namespace omop::test 