// tests/unit/transform/transformation_engine_test.cpp

#include <gtest/gtest.h>
#include "transform/transformation_engine.h"
#include "transform/vocabulary_service.h"
#include "transform/field_transformations.h"
#include "common/configuration.h"
#include "core/interfaces.h"
#include <memory>
#include <yaml-cpp/yaml.h>
#include "extract/database_connector.h"
#include <vector>
#include <string>
#include <any>
#include <map>
#include <iostream>

namespace omop {
class MockResultSet : public omop::extract::IResultSet {
public:
    MockResultSet(const std::vector<std::vector<std::any>>& data = {}) : data_(data), current_row_(-1) {}
    bool next() override {
        current_row_++;
        bool has_data = current_row_ < static_cast<int>(data_.size());
        // Debug output
        if (has_data) {
            std::cout << "MockResultSet: next() returned true, current_row_=" << current_row_
                      << ", data size=" << data_.size() << std::endl;
        } else {
            std::cout << "MockResultSet: next() returned false, current_row_=" << current_row_
                      << ", data size=" << data_.size() << std::endl;
        }
        return has_data;
    }
    std::any get_value(const std::string& column_name) const override {
        if (current_row_ >= 0 && current_row_ < static_cast<int>(data_.size())) {
            // Map column names to indices for common vocabulary queries
            if (column_name == "vocabulary_version" || column_name == "0") return data_[current_row_][0];
            if (column_name == "concept_id" || column_name == "0") return data_[current_row_][0];
            if (column_name == "concept_name" || column_name == "1") return data_[current_row_][1];
            if (column_name == "domain_id" || column_name == "2") return data_[current_row_][2];
            if (column_name == "vocabulary_id" || column_name == "3") return data_[current_row_][3];
            if (column_name == "concept_class_id" || column_name == "4") return data_[current_row_][4];
            if (column_name == "standard_concept" || column_name == "5") return data_[current_row_][5];
            if (column_name == "concept_code" || column_name == "6") return data_[current_row_][6];
            if (column_name == "valid_start_date" || column_name == "7") return data_[current_row_][7];
            if (column_name == "valid_end_date" || column_name == "8") return data_[current_row_][8];
        }
        // Return a default string value instead of empty any to avoid bad any cast
        return std::string("");
    }
    std::any get_value(size_t column_index) const override {
        if (current_row_ >= 0 && current_row_ < static_cast<int>(data_.size()) && column_index < data_[current_row_].size()) {
            return data_[current_row_][column_index];
        }
        // Return a default string value instead of empty any to avoid bad any cast
        return std::string("");
    }
    bool is_null(const std::string& column_name) const override {
        auto value = get_value(column_name);
        return !value.has_value() || value.type() == typeid(std::nullptr_t);
    }
    bool is_null(size_t column_index) const override {
        auto value = get_value(column_index);
        return !value.has_value() || value.type() == typeid(std::nullptr_t);
    }
    size_t column_count() const override {
        if (current_row_ >= 0 && current_row_ < static_cast<int>(data_.size())) {
            return data_[current_row_].size();
        }
        return 0;
    }
    std::string column_type(size_t) const override { return "text"; }
    std::string column_name(size_t) const override { return ""; }
    std::vector<std::string> get_column_names() const override { return {}; }
    omop::core::Record to_record() const override {
        omop::core::Record record;
        if (current_row_ >= 0 && current_row_ < static_cast<int>(data_.size())) {
            for (size_t i = 0; i < data_[current_row_].size(); ++i) {
                record.setField(std::to_string(i), data_[current_row_][i]);
            }
        }
        return record;
    }
private:
    std::vector<std::vector<std::any>> data_;
    int current_row_;
};

class MockPreparedStatement : public omop::extract::IPreparedStatement {
public:
    void bind(size_t param_index, const std::any& value) override {
        bound_params_[param_index] = value;
    }
    std::unique_ptr<omop::extract::IResultSet> execute_query() override {
        // Return appropriate mock data based on the bound parameters
        std::vector<std::vector<std::any>> mock_data;

        // Check if we have bound parameters for concept lookup
        if (bound_params_.find(1) != bound_params_.end() && bound_params_.find(2) != bound_params_.end()) {
            std::string concept_code = std::any_cast<std::string>(bound_params_[1]);
            std::string vocabulary_id = std::any_cast<std::string>(bound_params_[2]);

            // Return appropriate concept data based on the lookup
            if (vocabulary_id == "Gender") {
                if (concept_code == "M") {
                    mock_data = {{
                        8507,
                        std::string("MALE"),
                        std::string("Gender"),
                        std::string("Gender"),
                        std::string("Gender"),
                        std::string("S"),
                        std::string("M"),
                        std::string("1970-01-01"),
                        std::string("2099-12-31")
                    }};
                } else if (concept_code == "F") {
                    mock_data = {{
                        8532,
                        std::string("FEMALE"),
                        std::string("Gender"),
                        std::string("Gender"),
                        std::string("Gender"),
                        std::string("S"),
                        std::string("F"),
                        std::string("1970-01-01"),
                        std::string("2099-12-31")
                    }};
                }
            }
        }

        return std::make_unique<MockResultSet>(mock_data);
    }
    size_t execute_update() override { return 1; }
    void clear_parameters() override {
        bound_params_.clear();
    }
private:
    std::map<size_t, std::any> bound_params_;
};

class MockDatabaseConnection : public omop::extract::IDatabaseConnection {
public:
    void connect(const ConnectionParams&) override {}
    void disconnect() override {}
    bool is_connected() const override { return true; }
    std::unique_ptr<omop::extract::IResultSet> execute_query(const std::string& query) override {
        // Return appropriate mock data based on the query
        if (query.find("vocabulary_version") != std::string::npos) {
            // Vocabulary version query - return a single column result
            std::vector<std::vector<std::any>> data = {{std::string("v5.0")}};
            return std::make_unique<MockResultSet>(data);
        } else if (query.find("concept") != std::string::npos) {
            // Concept query - return some mock concepts
            std::vector<std::vector<std::any>> data = {
                {1, "Test Concept 1", "Condition", "SNOMED", "Clinical Finding", "S", "123456", "2020-01-01", "2099-12-31"},
                {2, "Test Concept 2", "Drug", "RxNorm", "Ingredient", "S", "789012", "2020-01-01", "2099-12-31"}
            };
            return std::make_unique<MockResultSet>(data);
        }
        return std::make_unique<MockResultSet>();
    }
    size_t execute_update(const std::string&) override { return 0; }
    std::unique_ptr<omop::extract::IPreparedStatement> prepare_statement(const std::string&) override {
        return std::make_unique<MockPreparedStatement>();
    }
    void begin_transaction() override {}
    void commit() override {}
    void rollback() override {}
    bool in_transaction() const override { return false; }
    std::string get_database_type() const override { return "mock"; }
    std::string get_version() const override { return "1.0"; }
    void set_query_timeout(int) override {}
    bool table_exists(const std::string&, const std::string& = "") const override { return true; }
    std::vector<std::string> get_table_names(const std::string& = "") const { return {}; }
    std::vector<std::string> get_column_names(const std::string&, const std::string& = "") const { return {}; }
};
} // namespace omop

namespace omop::transform::test {

// Helper function to set up test configuration
void setupTestConfiguration() {
    // Create a YAML configuration for testing
    YAML::Node config;

    // Add required source_database section
    YAML::Node source_db;
    source_db["host"] = "localhost";
    source_db["port"] = 5432;
    source_db["database"] = "test_source";
    source_db["username"] = "test_user";
    source_db["password"] = "test_pass";
    config["source_database"] = source_db;

    // Add required target_database section
    YAML::Node target_db;
    target_db["host"] = "localhost";
    target_db["port"] = 5432;
    target_db["database"] = "test_target";
    target_db["username"] = "test_user";
    target_db["password"] = "test_pass";
    config["target_database"] = target_db;

    // Add vocabulary mappings for the Gender vocabulary
    YAML::Node vocab_mappings;
    YAML::Node gender_vocab;
    gender_vocab["source_vocabulary"] = "Gender";
    gender_vocab["target_vocabulary"] = "Gender";
    vocab_mappings["Gender"] = gender_vocab;
    config["vocabulary_mappings"] = vocab_mappings;

    // Set up table mappings
    YAML::Node mappings;
    YAML::Node patient_mapping;
    patient_mapping["source_table"] = "patient";
    patient_mapping["target_table"] = "person";

    // Add transformations
    YAML::Node transformations;

    YAML::Node rule1;
    rule1["source_column"] = "patient_id";
    rule1["target_column"] = "person_id";
    rule1["type"] = "direct";
    transformations.push_back(rule1);

    YAML::Node rule2;
    rule2["source_column"] = "birth_date";
    rule2["target_column"] = "birth_datetime";
    rule2["type"] = "date_transform";
    YAML::Node date_params;
    date_params["format"] = "%Y-%m-%d";
    date_params["output_format"] = "%Y-%m-%d %H:%M:%S";
    date_params["add_time"] = true;
    rule2["parameters"] = date_params;
    transformations.push_back(rule2);

    YAML::Node rule3;
    rule3["source_column"] = "gender";
    rule3["target_column"] = "gender_concept_id";
    rule3["type"] = "vocabulary_mapping";
    rule3["vocabulary"] = "Gender";
    rule3["default_value"] = 0;
    transformations.push_back(rule3);

    patient_mapping["transformations"] = transformations;

    // Add filters
    YAML::Node filters;
    YAML::Node filter1;
    filter1["field"] = "patient_id";
    filter1["operator"] = "not_null";
    filters.push_back(filter1);
    patient_mapping["filters"] = filters;

    // Add validations
    YAML::Node validations;
    YAML::Node validation1;
    validation1["field"] = "person_id";
    validation1["type"] = "required";
    validations.push_back(validation1);
    patient_mapping["validations"] = validations;

    mappings["patient"] = patient_mapping;
    config["table_mappings"] = mappings;

    // Load configuration into the singleton
    auto& config_mgr = common::Config::instance();
    config_mgr.load_config_from_string(YAML::Dump(config));
}

class TransformationEngineTest : public ::testing::Test {
protected:
    void SetUp() override {
        context_ = std::make_unique<core::ProcessingContext>();
        engine_ = std::make_unique<TransformationEngine>();

        // Set up test configuration
        setupTestConfiguration();

        // Initialize vocabulary service for testing
        if (!VocabularyServiceManager::is_initialised()) {
            auto mock_conn = std::make_unique<omop::MockDatabaseConnection>();
            VocabularyServiceManager::initialize(std::move(mock_conn));
        }
    }

    void TearDown() override {
        engine_.reset();
        context_.reset();
    }

    std::unique_ptr<core::ProcessingContext> context_;
    std::unique_ptr<TransformationEngine> engine_;
};

// Test case: Initialize transformation engine with valid table configuration
TEST_F(TransformationEngineTest, InitializeEngine) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");

    EXPECT_NO_THROW(engine_->initialize(config, *context_));
}

// Test case: Initialize transformation engine without table name should throw configuration exception
TEST_F(TransformationEngineTest, InitializeEngineNoTableName) {
    std::unordered_map<std::string, std::any> config;

    EXPECT_THROW(
        engine_->initialize(config, *context_),
        common::ConfigurationException
    );
}

// Test case: Initialize transformation engine with non-existent table should throw configuration exception
TEST_F(TransformationEngineTest, InitializeEngineInvalidTable) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("non_existent_table");

    EXPECT_THROW(
        engine_->initialize(config, *context_),
        common::ConfigurationException
    );
}

// Test transformation of a single record with UK-localised data formats
TEST_F(TransformationEngineTest, TransformRecord) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);

    core::Record input_record;
    input_record.setField("patient_id", 12345);
    input_record.setField("birth_date", std::string("15/01/1990")); // UK date format: DD/MM/YYYY
    input_record.setField("gender", std::string("M"));
    input_record.setField("nhs_number", std::string("**********")); // Valid UK NHS number
    input_record.setField("postcode", std::string("SW1A 1AA")); // UK postcode format

    auto result = engine_->transform(input_record, *context_);

    ASSERT_TRUE(result.has_value());

    // Check transformed fields
    auto person_id = result->getFieldOptional("person_id");
    ASSERT_TRUE(person_id.has_value());
    EXPECT_EQ(12345, std::any_cast<int>(*person_id));

    auto birth_datetime = result->getFieldOptional("birth_datetime");
    ASSERT_TRUE(birth_datetime.has_value());
    EXPECT_EQ("1990-01-15 00:00:00", std::any_cast<std::string>(*birth_datetime)); // Output normalized to ISO format
    
    // Validate UK-specific fields if present
    auto nhs_number = result->getFieldOptional("nhs_number");
    if (nhs_number.has_value()) {
        std::string nhs_str = std::any_cast<std::string>(*nhs_number);
        EXPECT_EQ("**********", nhs_str);
    }
}

// Test transforming record with filter
TEST_F(TransformationEngineTest, TransformRecordFiltered) {
    std::cout << "[TEST] Starting TransformRecordFiltered test" << std::endl;
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);

    core::Record input_record;
    // Intentionally omit 'patient_id' so the record is filtered out by the 'not_null' filter
    input_record.setField("birth_date", std::string("1990-01-15"));
    input_record.setField("gender", std::string("M"));

    std::cout << "[TEST] About to call engine_->transform()" << std::endl;
    auto result = engine_->transform(input_record, *context_);
    std::cout << "[TEST] transform() returned: " << (result.has_value() ? "HAS VALUE" : "nullopt") << std::endl;

    EXPECT_FALSE(result.has_value());
}

// Test transforming batch of records
TEST_F(TransformationEngineTest, TransformBatch) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);

    core::RecordBatch batch;

    // Add valid record
    core::Record record1;
    record1.setField("patient_id", 1);
    record1.setField("birth_date", std::string("1990-01-15"));
    record1.setField("gender", std::string("F")); // Add required field
    batch.addRecord(record1);

    // Add record missing patient_id (should be filtered)
    core::Record record2;
    record2.setField("birth_date", std::string("1985-05-20"));
    record2.setField("gender", std::string("M")); // Add required field
    batch.addRecord(record2);

    // Add another valid record
    core::Record record3;
    record3.setField("patient_id", 3);
    record3.setField("birth_date", std::string("2000-12-31"));
    record3.setField("gender", std::string("M")); // Add required field
    batch.addRecord(record3);

    auto result_batch = engine_->transform_batch(batch, *context_);

    // Should have 2 records (one filtered out)
    EXPECT_EQ(2, result_batch.size());
}

// Test transformation with validation
TEST_F(TransformationEngineTest, TransformWithValidation) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);

    // Enable validation
    context_->set_data("validate_records", true);
    context_->set_data("strict_validation", false);

    core::Record input_record;
    input_record.setField("patient_id", 12345);
    input_record.setField("birth_date", std::string("1990-01-15"));
    input_record.setField("gender", std::string("M")); // Add required field

    auto result = engine_->transform(input_record, *context_);

    ASSERT_TRUE(result.has_value());
    // Should transform even with validation warnings
}

// Test transformation with strict validation
TEST_F(TransformationEngineTest, TransformWithStrictValidation) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);

    // Enable strict validation
    context_->set_data("validate_records", true);
    context_->set_data("strict_validation", true);

    core::Record input_record;
    // Intentionally omit 'birth_date' so transformation fails strict validation
    input_record.setField("patient_id", 12345);
    input_record.setField("gender", std::string("M"));

    auto result = engine_->transform(input_record, *context_);

    EXPECT_FALSE(result.has_value());
}

// Test getting engine type
TEST_F(TransformationEngineTest, GetEngineType) {
    EXPECT_EQ("omop_transformation_engine", engine_->get_type());
}

// Test validate method
TEST_F(TransformationEngineTest, ValidateRecord) {
    core::Record record;
    record.setField("test_field", std::string("test_value"));

    auto validation_result = engine_->validate(record);

    // Basic validation should pass
    EXPECT_TRUE(validation_result.is_valid());
}

// Test get statistics
TEST_F(TransformationEngineTest, GetStatistics) {
    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    engine_->initialize(config, *context_);

    // Transform some records
    core::Record record;
    record.setField("patient_id", 1);
    record.setField("birth_date", std::string("1990-01-15"));
    record.setField("gender", std::string("F")); // Add required field
    engine_->transform(record, *context_);

    auto stats = engine_->get_statistics();

    EXPECT_TRUE(stats.find("records_transformed") != stats.end());
    EXPECT_TRUE(stats.find("records_filtered") != stats.end());
    EXPECT_TRUE(stats.find("validation_errors") != stats.end());
    EXPECT_TRUE(stats.find("transformation_errors") != stats.end());
}

// Test registering custom transformation
TEST_F(TransformationEngineTest, RegisterCustomTransformation) {
    auto factory = []() {
        return std::make_unique<DirectTransformation>();
    };

    EXPECT_NO_THROW(engine_->register_transformation("custom_direct", factory));
}

// Test transformation error handling
TEST_F(TransformationEngineTest, TransformationError) {
    std::cout << "[TEST] Starting TransformationError test" << std::endl;

    std::unordered_map<std::string, std::any> config;
    config["table_name"] = std::string("patient");
    std::cout << "[TEST] About to initialize engine with table_name=patient" << std::endl;
    engine_->initialize(config, *context_);
    std::cout << "[TEST] Engine initialized successfully" << std::endl;

    core::Record input_record;
    input_record.setField("patient_id", 12345);
    // Invalid date format will cause transformation error
    input_record.setField("birth_date", std::string("invalid-date"));
    input_record.setField("gender", std::string("M"));

    std::cout << "[TEST] About to call engine_->transform()" << std::endl;
    EXPECT_THROW(
        engine_->transform(input_record, *context_),
        common::TransformationException
    );
}

// Test TransformationEngineFactory
TEST_F(TransformationEngineTest, TransformationEngineFactory) {
    auto& config = common::Config::instance();

    auto engine = TransformationEngineFactory::create_for_table("patient", config);
    ASSERT_NE(nullptr, engine);
    EXPECT_EQ("omop_transformation_engine", engine->get_type());
}

// Test multiple field transformations
// This test verifies that the engine can apply multiple field transformations (concatenation and numeric) using the current API.
TEST_F(TransformationEngineTest, MultipleFieldTransformations) {
    // Register all required transformations
    omop::transform::register_all_transformations();

    // Create a custom TableMapping using YAML (simulate config)
    YAML::Node mapping_node;
    mapping_node["source_table"] = "test";
    mapping_node["target_table"] = "target";
    YAML::Node transformations = YAML::Node(YAML::NodeType::Sequence);

    // Concatenation transformation: full_name = first_name + " " + last_name
    YAML::Node concat_rule;
    concat_rule["source_column"] = "first_name";
    concat_rule["target_column"] = "full_name";
    concat_rule["type"] = "string_concatenation";
    concat_rule["parameters"]["fields"] = YAML::Node(YAML::NodeType::Sequence);
    concat_rule["parameters"]["fields"].push_back("first_name");
    concat_rule["parameters"]["fields"].push_back("last_name");
    concat_rule["parameters"]["separator"] = " ";
    transformations.push_back(concat_rule);

    // Numeric transformation: height_m = height_cm / 100.0
    YAML::Node numeric_rule;
    numeric_rule["source_column"] = "height_cm";
    numeric_rule["target_column"] = "height_m";
    numeric_rule["type"] = "numeric_transform";
    numeric_rule["parameters"]["operation"] = "divide";
    numeric_rule["parameters"]["operand"] = 100.0;
    transformations.push_back(numeric_rule);

    mapping_node["transformations"] = transformations;
    common::TableMapping mapping(mapping_node);

    // Inject the mapping into the engine
    engine_->initialize({{"table_name", std::string("test")}, {"test_mode", true}}, *context_);
    // Directly set the mapping for test (simulate custom mapping)
    engine_->set_current_mapping_for_test(mapping);

    // Create input record
    core::Record input_record;
    input_record.setField("first_name", std::string("John"));
    input_record.setField("last_name", std::string("Smith"));
    input_record.setField("height_cm", 180.0);

    // Transform
    auto result = engine_->transform(input_record, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("full_name")), "John Smith");
    EXPECT_NEAR(std::any_cast<double>(result->getField("height_m")), 1.8, 1e-6);
}

// Test conditional transformation in engine
// This test verifies that the engine can apply a conditional transformation using the current API.
TEST_F(TransformationEngineTest, ConditionalTransformationInEngine) {
    // Register all required transformations
    omop::transform::register_all_transformations();

    // Create a custom TableMapping using YAML (simulate config)
    YAML::Node mapping_node;
    mapping_node["source_table"] = "test";
    mapping_node["target_table"] = "target";
    YAML::Node transformations = YAML::Node(YAML::NodeType::Sequence);

    // Conditional transformation: status_concept_id based on status_code
    YAML::Node cond_rule;
    cond_rule["source_column"] = "status_code";
    cond_rule["target_column"] = "status_concept_id";
    cond_rule["type"] = "conditional";
    YAML::Node cond_params;
    YAML::Node conds = YAML::Node(YAML::NodeType::Sequence);
    YAML::Node cond1;
    cond1["operator"] = "equals";
    cond1["value"] = "A";
    cond1["then"] = "4188539"; // Active
    conds.push_back(cond1);
    YAML::Node cond2;
    cond2["operator"] = "equals";
    cond2["value"] = "I";
    cond2["then"] = "4188540"; // Inactive
    conds.push_back(cond2);
    cond_params["conditions"] = conds;
    cond_params["default"] = "0";
    cond_rule["parameters"] = cond_params;
    transformations.push_back(cond_rule);

    mapping_node["transformations"] = transformations;
    common::TableMapping mapping(mapping_node);

    // Inject the mapping into the engine
    engine_->initialize({{"table_name", std::string("test")}, {"test_mode", true}}, *context_);
    // Directly set the mapping for test (simulate custom mapping)
    engine_->set_current_mapping_for_test(mapping);

    // Create input record (UK NHS status codes)
    core::Record input_record;
    input_record.setField("status_code", std::string("A"));

    // Transform
    auto result = engine_->transform(input_record, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("status_concept_id")), "4188539");

    // Test with inactive code
    input_record.setField("status_code", std::string("I"));
    result = engine_->transform(input_record, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("status_concept_id")), "4188540");

    // Test with unknown code
    input_record.setField("status_code", std::string("X"));
    result = engine_->transform(input_record, *context_);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(std::any_cast<std::string>(result->getField("status_concept_id")), "0");
}

// Test direct DateTransformation exception
TEST_F(TransformationEngineTest, DirectDateTransformationException) {
    std::cout << "[TEST] Starting DirectDateTransformationException test" << std::endl;

    // Create a DateTransformation directly
    auto date_transform = std::make_unique<DateTransformation>();

    // Configure it
    YAML::Node params;
    params["format"] = "%Y-%m-%d";
    date_transform->configure(params);

    // Create a processing context
    core::ProcessingContext context;

    // Test with invalid date
    std::any invalid_date = std::string("invalid-date");

    std::cout << "[TEST] About to call DateTransformation::transform() directly" << std::endl;
    EXPECT_THROW(
        date_transform->transform(invalid_date, context),
        common::TransformationException
    );
    std::cout << "[TEST] Direct DateTransformation test completed" << std::endl;
}

// Test batch transformation with errors
// This test verifies that the engine processes a batch with both valid and invalid records, only returning valid results and logging errors.
TEST_F(TransformationEngineTest, BatchTransformationWithErrors) {
    // Register all required transformations
    omop::transform::register_all_transformations();

    // Create a custom TableMapping using YAML (simulate config)
    YAML::Node mapping_node;
    mapping_node["source_table"] = "patient";
    mapping_node["target_table"] = "person";
    YAML::Node transformations = YAML::Node(YAML::NodeType::Sequence);

    // Direct transformation: person_id = patient_id
    YAML::Node id_rule;
    id_rule["source_column"] = "patient_id";
    id_rule["target_column"] = "person_id";
    id_rule["type"] = "direct";
    transformations.push_back(id_rule);

    // Date transformation: birth_datetime = birth_date (UK format)
    YAML::Node date_rule;
    date_rule["source_column"] = "birth_date";
    date_rule["target_column"] = "birth_datetime";
    date_rule["type"] = "date_transform";
    date_rule["parameters"]["format"] = "%Y-%m-%d";
    transformations.push_back(date_rule);

    mapping_node["transformations"] = transformations;
    common::TableMapping mapping(mapping_node);

    // Inject the mapping into the engine
    engine_->initialize({{"table_name", std::string("patient")}, {"test_mode", true}}, *context_);
    engine_->set_current_mapping_for_test(mapping);

    core::RecordBatch batch;

    // Add record with transformation error (invalid date)
    core::Record record1;
    record1.setField("patient_id", 1);
    record1.setField("birth_date", std::string("invalid-date"));
    batch.addRecord(record1);

    // Add valid record (UK date)
    core::Record record2;
    record2.setField("patient_id", 2);
    record2.setField("birth_date", std::string("1990-01-15"));
    batch.addRecord(record2);

    auto result_batch = engine_->transform_batch(batch, *context_);

    // Should only have the valid record
    ASSERT_EQ(1, result_batch.size());
    EXPECT_EQ(std::any_cast<int>(result_batch.getRecord(0).getField("person_id")), 2);
    EXPECT_EQ(std::any_cast<std::string>(result_batch.getRecord(0).getField("birth_datetime")), "1990-01-15 00:00:00");

    // Check that error was logged
    EXPECT_GT(context_->error_count(), 0);
}
}