// tests/unit/transform/anonymization_transformations_unit_test.cpp

#include <gtest/gtest.h>
#include "transform/anonymization_transformations.h"
#include "transform/transformation_engine.h"
#include "common/configuration.h"
#include "core/interfaces.h"
#include <memory>
#include <yaml-cpp/yaml.h>
#include <chrono>
#include <string>
#include <any>
#include <nlohmann/json.hpp>

using namespace omop::transform;
using namespace omop::core;

/**
 * Test fixture for anonymization transformations
 */
class AnonymizationTransformationsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a mock processing context
        context_ = std::make_unique<ProcessingContext>();
        context_->set_data("test_mode", true);
    }

    void TearDown() override {
        context_.reset();
    }

    std::unique_ptr<ProcessingContext> context_;
};

/**
 * Test NHS Number Anonymization
 */
TEST_F(AnonymizationTransformationsTest, NHSNumberAnonymization) {
    NHSNumberAnonymizer anonymizer;
    
    // Configure anonymizer
    YAML::Node config;
    config["global_salt"] = "test_salt_123";
    config["hash_iterations"] = 10000;
    anonymizer.configure(config);
    
    // Test valid NHS number
    std::string nhs_number = "**********"; // Valid NHS number format
    std::any input = nhs_number;
    
    ASSERT_TRUE(anonymizer.validate_input(input));
    
    // Apply anonymization
    auto result = anonymizer.transform_detailed(input, *context_);
    
    EXPECT_TRUE(result.is_success());
    EXPECT_FALSE(anonymizer.contains_personal_data());
    EXPECT_FALSE(anonymizer.is_reversible());
    EXPECT_EQ(anonymizer.get_compliance_level(), AnonymizationLevel::FULLY_ANONYMIZED);
    EXPECT_EQ(anonymizer.get_type(), "nhs_number_anonymizer");
    
    // Result should be a string hash, not the original NHS number
    ASSERT_TRUE(result.value.has_value());
    EXPECT_NE(std::any_cast<std::string>(result.value), nhs_number);
    
    // Same input should produce same output (deterministic)
    auto result2 = anonymizer.transform_detailed(input, *context_);
    EXPECT_TRUE(result2.is_success());
    EXPECT_EQ(std::any_cast<std::string>(result.value), 
              std::any_cast<std::string>(result2.value));
}

/**
 * Test Date to Age Anonymization
 */
TEST_F(AnonymizationTransformationsTest, DateToAgeAnonymization) {
    DateToAgeAnonymizer anonymizer;
    
    // Configure anonymizer
    YAML::Node config;
    config["use_current_date_as_reference"] = false;
    config["reference_date"] = "2025-01-01"; // Fixed reference date for testing
    anonymizer.configure(config);
    
    // Test birth date (should produce age)
    std::string birth_date = "1990-05-15"; // 34 years old as of 2025-01-01
    std::any input = birth_date;
    
    ASSERT_TRUE(anonymizer.validate_input(input));
    
    // Apply anonymization
    auto result = anonymizer.transform_detailed(input, *context_);
    
    EXPECT_TRUE(result.is_success());
    EXPECT_FALSE(anonymizer.contains_personal_data());
    EXPECT_FALSE(anonymizer.is_reversible());
    EXPECT_EQ(anonymizer.get_compliance_level(), AnonymizationLevel::FULLY_ANONYMIZED);
    EXPECT_EQ(anonymizer.get_type(), "date_to_age_anonymizer");
    
    // Result should contain age information, not the original date
    ASSERT_TRUE(result.value.has_value());
    // The result might be a JSON object or structured data representing age
}

/**
 * Test UK Postcode to Region Anonymization
 */
TEST_F(AnonymizationTransformationsTest, PostcodeToRegionAnonymization) {
    PostcodeToRegionAnonymizer anonymizer;
    
    // Configure anonymizer
    YAML::Node config;
    config["minimum_population_size"] = 100000;
    config["use_nhs_regions"] = true;
    anonymizer.configure(config);
    
    // Test UK postcode
    std::string postcode = "SW1A 1AA"; // Westminster postcode
    std::any input = postcode;
    
    ASSERT_TRUE(anonymizer.validate_input(input));
    
    // Apply anonymization
    auto result = anonymizer.transform_detailed(input, *context_);
    
    EXPECT_TRUE(result.is_success());
    EXPECT_FALSE(anonymizer.contains_personal_data());
    EXPECT_FALSE(anonymizer.is_reversible());
    EXPECT_EQ(anonymizer.get_compliance_level(), AnonymizationLevel::FULLY_ANONYMIZED);
    EXPECT_EQ(anonymizer.get_type(), "postcode_to_region_anonymizer");
    
    // Result should be a region JSON object, not the original postcode
    ASSERT_TRUE(result.value.has_value());
    // Should be a JSON object containing region information
    ASSERT_TRUE(result.value.type() == typeid(nlohmann::json));
    auto result_json = std::any_cast<nlohmann::json>(result.value);
    EXPECT_TRUE(result_json.contains("region_code"));
    EXPECT_TRUE(result_json.contains("region_name"));
    // Should not contain the original postcode
    EXPECT_NE(result_json["region_code"], postcode);
}

/**
 * Test Secure Hash Anonymization
 */
TEST_F(AnonymizationTransformationsTest, SecureHashAnonymization) {
    SecureHashAnonymizer anonymizer;
    
    // Configure anonymizer
    YAML::Node config;
    config["algorithm"] = "SHA256";
    config["iterations"] = 10000;
    config["global_salt"] = "test_global_salt";
    config["field_specific_salt"] = "field_salt";
    anonymizer.configure(config);
    
    // Test with string input
    std::string sensitive_data = "patient_identifier_12345";
    std::any input = sensitive_data;
    
    ASSERT_TRUE(anonymizer.validate_input(input));
    
    // Apply anonymization
    auto result = anonymizer.transform_detailed(input, *context_);
    
    EXPECT_TRUE(result.is_success());
    EXPECT_FALSE(anonymizer.contains_personal_data());
    EXPECT_FALSE(anonymizer.is_reversible());
    EXPECT_EQ(anonymizer.get_compliance_level(), AnonymizationLevel::FULLY_ANONYMIZED);
    EXPECT_EQ(anonymizer.get_type(), "secure_hash_anonymizer");
    
    // Result should be a hex string hash, not the original data
    ASSERT_TRUE(result.value.has_value());
    auto hash_result = std::any_cast<std::string>(result.value);
    EXPECT_NE(hash_result, sensitive_data);
    EXPECT_GT(hash_result.length(), 0);
}

/**
 * Test Research Pseudonymization
 */
TEST_F(AnonymizationTransformationsTest, ResearchPseudonymization) {
    ResearchPseudonymizer pseudonymizer;
    
    // Configure pseudonymizer
    YAML::Node config;
    config["study_id"] = "STUDY_001";
    config["research_key"] = "research_key_123";
    config["pseudonym_format"] = "P{:08X}";
    config["time_limited"] = false;
    pseudonymizer.configure(config);
    
    // Test with patient identifier
    std::string patient_id = "patient_abc_123";
    std::any input = patient_id;
    
    ASSERT_TRUE(pseudonymizer.validate_input(input));
    
    // Apply pseudonymization
    auto result = pseudonymizer.transform_detailed(input, *context_);
    
    EXPECT_TRUE(result.is_success());
    EXPECT_FALSE(pseudonymizer.contains_personal_data()); // Pseudonym doesn't contain personal data directly
    EXPECT_FALSE(pseudonymizer.is_reversible()); // Cannot be reversed without key
    EXPECT_EQ(pseudonymizer.get_compliance_level(), AnonymizationLevel::PSEUDONYMIZED);
    EXPECT_EQ(pseudonymizer.get_type(), "research_pseudonymizer");
    
    // Result should be a pseudonym, not the original identifier
    ASSERT_TRUE(result.value.has_value());
    auto pseudonym = std::any_cast<std::string>(result.value);
    EXPECT_NE(pseudonym, patient_id);
    EXPECT_TRUE(pseudonym.starts_with("P")); // Should match format
    
    // Same input should produce same pseudonym (consistency)
    auto result2 = pseudonymizer.transform_detailed(input, *context_);
    EXPECT_TRUE(result2.is_success());
    EXPECT_EQ(std::any_cast<std::string>(result.value), 
              std::any_cast<std::string>(result2.value));
}

/**
 * Test Composite Anonymization
 */
TEST_F(AnonymizationTransformationsTest, CompositeAnonymization) {
    CompositeAnonymizer composite_anonymizer;
    
    // Configure composite anonymizer
    YAML::Node config;
    config["enabled"] = true;
    composite_anonymizer.configure(config);
    
    // Add field-specific anonymizers
    composite_anonymizer.add_field_anonymizer("nhs_number", 
        std::make_unique<NHSNumberAnonymizer>());
    
    auto date_anonymizer = std::make_unique<DateToAgeAnonymizer>();
    YAML::Node date_config;
    date_config["use_current_date_as_reference"] = false;
    date_config["reference_date"] = "2025-01-01";
    date_anonymizer->configure(date_config);
    composite_anonymizer.add_field_anonymizer("birth_date", std::move(date_anonymizer));
    
    // Test with a record-like structure (simulate using JSON)
    // In practice, this would be a core::Record
    nlohmann::json record_data;
    record_data["nhs_number"] = "**********";
    record_data["birth_date"] = "1990-05-15";
    record_data["other_field"] = "some_value";
    
    std::any input = record_data;
    
    ASSERT_TRUE(composite_anonymizer.validate_input(input));
    
    // Apply composite anonymization
    auto result = composite_anonymizer.transform_detailed(input, *context_);
    
    EXPECT_TRUE(result.is_success());
    EXPECT_EQ(composite_anonymizer.get_type(), "composite_anonymizer");
    
    // Check that overall compliance is appropriate
    auto compliance_level = composite_anonymizer.get_compliance_level();
    EXPECT_TRUE(compliance_level == AnonymizationLevel::FULLY_ANONYMIZED || 
               compliance_level == AnonymizationLevel::PSEUDONYMIZED);
}

/**
 * Test Anonymization Factory
 */
TEST_F(AnonymizationTransformationsTest, AnonymizationFactory) {
    // Test factory creation of different anonymization types
    auto available_types = AnonymizationTransformationFactory::get_available_types();
    
    EXPECT_GT(available_types.size(), 0);
    EXPECT_TRUE(std::find(available_types.begin(), available_types.end(), 
                         "nhs_number_anonymizer") != available_types.end());
    EXPECT_TRUE(std::find(available_types.begin(), available_types.end(), 
                         "date_to_age_anonymizer") != available_types.end());
    EXPECT_TRUE(std::find(available_types.begin(), available_types.end(), 
                         "postcode_to_region_anonymizer") != available_types.end());
    EXPECT_TRUE(std::find(available_types.begin(), available_types.end(), 
                         "secure_hash_anonymizer") != available_types.end());
    EXPECT_TRUE(std::find(available_types.begin(), available_types.end(), 
                         "research_pseudonymizer") != available_types.end());
    EXPECT_TRUE(std::find(available_types.begin(), available_types.end(), 
                         "composite_anonymizer") != available_types.end());
    
    // Test creating anonymizers through factory
    for (const auto& type : available_types) {
        auto anonymizer = AnonymizationTransformationFactory::create(type);
        ASSERT_NE(anonymizer, nullptr);
        EXPECT_EQ(anonymizer->get_type(), type);
    }
}

/**
 * Test Integration with TransformationEngine
 */
TEST_F(AnonymizationTransformationsTest, TransformationEngineIntegration) {
    // Create transformation engine
    TransformationEngine engine;
    
    // Check that anonymization is mandatory by default
    EXPECT_TRUE(engine.is_anonymization_mandatory());
    
    // Configure anonymization
    YAML::Node anon_config;
    anon_config["enabled"] = true;
    anon_config["compliance_level"] = "fully_anonymized";
    anon_config["uk_healthcare_mode"] = true;
    engine.set_anonymization_config(anon_config);
    
    // Initialize engine in test mode
    std::unordered_map<std::string, std::any> config;
    config["test_mode"] = std::any(true);
    config["table_name"] = std::any(std::string("person"));
    // Don't pass anonymization_config - let it use defaults
    
    // This should initialize successfully with anonymization
    EXPECT_NO_THROW(engine.initialize(config, *context_));
    
    // Test that anonymization transformations are available
    auto& registry = TransformationRegistry::instance();
    EXPECT_TRUE(registry.has_transformation("nhs_number_anonymizer"));
    EXPECT_TRUE(registry.has_transformation("date_to_age_anonymizer"));
    EXPECT_TRUE(registry.has_transformation("postcode_to_region_anonymizer"));
    EXPECT_TRUE(registry.has_transformation("secure_hash_anonymizer"));
    EXPECT_TRUE(registry.has_transformation("research_pseudonymizer"));
    EXPECT_TRUE(registry.has_transformation("composite_anonymizer"));
}