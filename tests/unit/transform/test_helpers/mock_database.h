#pragma once

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <any>
#include "extract/database_connector.h"

namespace omop::test {

/**
 * Simple mock database connection for unit testing vocabulary service
 */
class MockDatabaseConnection : public extract::IDatabaseConnection {
public:
    MockDatabaseConnection() = default;
    virtual ~MockDatabaseConnection() = default;

    // Store query results for mock responses
    using QueryResult = std::vector<std::vector<std::any>>;
    
    void add_query_result(const std::string& query, const QueryResult& result) {
        query_results_[query] = result;
    }

    // IDatabaseConnection interface
    void connect(const ConnectionParams& params) override {
        connected_ = true;
    }

    void disconnect() override {
        connected_ = false;
    }

    bool is_connected() const override {
        return connected_;
    }

    std::unique_ptr<extract::IResultSet> execute_query(const std::string& query) override {
        // For unit tests, we don't need full query execution
        // Return nullptr for now - tests can be simplified
        return nullptr;
    }

    size_t execute_update(const std::string& query) override {
        // Mock implementation - return 1 (one row affected)
        return 1;
    }

    std::unique_ptr<extract::IPreparedStatement> prepare_statement(const std::string& query) override {
        // Mock implementation - return nullptr
        return nullptr;
    }

    void begin_transaction() override {}
    void commit() override {}
    void rollback() override {}

    std::string get_database_type() const override {
        return "mock";
    }

    std::string get_version() const override {
        return "1.0.0";
    }

    void set_query_timeout(int seconds) override {}

    bool table_exists(const std::string& table_name, const std::string& schema = "") const override {
        return true; // Mock always returns true
    }

    bool in_transaction() const override {
        return false;
    }

private:
    bool connected_ = false;
    std::map<std::string, QueryResult> query_results_;
};

} // namespace omop::test