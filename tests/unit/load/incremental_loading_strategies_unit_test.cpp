#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "load/loader_strategies.h"
#include "extract/database_connector.h"
#include "core/record.h"
#include "common/logging.h"
#include <filesystem>
#include <fstream>
#include <chrono>
#include <thread>

namespace omop::test::load {

/**
 * @brief Test fixture for incremental loading strategies
 *
 * Tests CDC, watermarking, and delta loading functionality with UK NHS data scenarios.
 */
class IncrementalLoadingStrategiesTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directories for testing
        test_dir_ = std::filesystem::temp_directory_path() / "omop_incremental_test";
        watermark_dir_ = test_dir_ / "watermarks";
        std::filesystem::create_directories(watermark_dir_);
        
        // Initialize logger
        logger_ = common::Logger::get("incremental-loading-test");
        
        // Set up basic loading configuration
        config_.strategy = omop::load::LoadingStrategy::DeltaLoad;
        config_.mode = omop::load::LoadingMode::Upsert;
        config_.batch_size = 100;
        config_.enable_cdc = true;
        config_.enable_hash_comparison = true;
        config_.target_table = "person";
        config_.key_columns = {"person_id"};
        config_.tracking_columns = {"birth_datetime", "gender_concept_id", "race_concept_id"};
        config_.watermark_column = "updated_at";
        config_.watermark_file = (watermark_dir_ / "person.watermark").string();
        config_.cdc_table_prefix = "cdc_";
        
        // Initialize processing context
        context_.set_processing_stage(core::ProcessingContext::Stage::Loading);
        context_.set_batch_size(100);
        
        // Create mock database connector
        mock_connector_ = std::make_shared<MockDatabaseConnector>();
    }
    
    void TearDown() override {
        if (std::filesystem::exists(test_dir_)) {
            std::filesystem::remove_all(test_dir_);
        }
    }
    
    // Mock database connector for testing
    class MockDatabaseConnector : public omop::extract::IDatabaseConnector {
    public:
        MOCK_METHOD(std::unique_ptr<omop::extract::IDatabaseConnection>, connect, (), (override));
        MOCK_METHOD(void, disconnect, (), (override));
        MOCK_METHOD(bool, is_connected, (), (const, override));
        MOCK_METHOD(std::string, get_connection_string, (), (const, override));
        MOCK_METHOD(std::vector<std::string>, get_table_names, (), (override));
        MOCK_METHOD(std::vector<std::string>, get_column_names, (const std::string&), (override));
        MOCK_METHOD(bool, table_exists, (const std::string&), (override));
        MOCK_METHOD(std::unique_ptr<omop::extract::IResultSet>, execute_query, (const std::string&), (override));
        MOCK_METHOD(bool, execute_non_query, (const std::string&), (override));
        MOCK_METHOD(std::unique_ptr<omop::extract::IPreparedStatement>, prepare_statement, (const std::string&), (override));
        MOCK_METHOD(bool, begin_transaction, (), (override));
        MOCK_METHOD(bool, commit_transaction, (), (override));
        MOCK_METHOD(bool, rollback_transaction, (), (override));
    };
    
    // Helper method to create UK NHS test record
    omop::core::Record create_uk_nhs_record(int64_t person_id, const std::string& nhs_number, 
                                           const std::string& gender = "M", 
                                           const std::string& birth_date = "1970-01-01",
                                           const std::string& postcode = "SW1A 1AA") {
        omop::core::Record record;
        record.set_field("person_id", person_id);
        record.set_field("nhs_number", nhs_number);
        record.set_field("gender_source_value", gender);
        record.set_field("birth_datetime", birth_date + " 00:00:00");
        record.set_field("location_source_value", postcode);
        record.set_field("updated_at", std::chrono::system_clock::now());
        
        // Add UK-specific fields
        record.set_field("country_concept_id", 4330343); // United Kingdom
        record.set_field("gender_concept_id", gender == "M" ? 8507 : 8532);
        record.set_field("race_concept_id", 8527); // White
        
        return record;
    }
    
    // Helper method to create watermark file with timestamp
    void create_watermark_file(const std::string& table_name, 
                              std::chrono::system_clock::time_point timestamp) {
        std::filesystem::path watermark_path = watermark_dir_ / (table_name + ".watermark");
        std::ofstream file(watermark_path);
        
        nlohmann::json watermark_json;
        watermark_json["table"] = table_name;
        watermark_json["timestamp"] = std::chrono::system_clock::to_time_t(timestamp);
        watermark_json["last_updated"] = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
        watermark_json["values"] = nlohmann::json::object();
        
        file << watermark_json.dump(2);
    }
    
protected:
    std::filesystem::path test_dir_;
    std::filesystem::path watermark_dir_;
    omop::load::LoadingConfig config_;
    omop::core::ProcessingContext context_;
    std::shared_ptr<MockDatabaseConnector> mock_connector_;
    std::shared_ptr<spdlog::logger> logger_;
};

// Test DeltaLoadingStrategy initialization
TEST_F(IncrementalLoadingStrategiesTest, DeltaLoadingStrategy_Initialization) {
    omop::load::DeltaLoadingStrategy strategy;
    
    EXPECT_TRUE(strategy.initialize(config_, context_));
    EXPECT_EQ(strategy.get_strategy_type(), omop::load::LoadingStrategy::DeltaLoad);
    
    auto supported_modes = strategy.get_supported_modes();
    EXPECT_TRUE(std::find(supported_modes.begin(), supported_modes.end(), 
                         omop::load::LoadingMode::Upsert) != supported_modes.end());
    EXPECT_TRUE(std::find(supported_modes.begin(), supported_modes.end(), 
                         omop::load::LoadingMode::Insert) != supported_modes.end());
}

// Test watermark initialization and persistence
TEST_F(IncrementalLoadingStrategiesTest, DeltaLoadingStrategy_WatermarkPersistence) {
    omop::load::DeltaLoadingStrategy strategy;
    ASSERT_TRUE(strategy.initialize(config_, context_));
    
    // Test initial watermark creation
    EXPECT_TRUE(strategy.initialize_watermarks());
    
    // Create some test data with timestamps
    auto now = std::chrono::system_clock::now();
    auto earlier = now - std::chrono::hours(1);
    create_watermark_file("person", earlier);
    
    // Test watermark loading
    EXPECT_TRUE(strategy.initialize_watermarks());
    auto last_watermark = strategy.get_last_watermark();
    
    // Watermark should be loaded from file
    EXPECT_GT(std::chrono::system_clock::to_time_t(last_watermark), 
              std::chrono::system_clock::to_time_t(earlier) - 60); // Allow 60 second tolerance
    
    // Test watermark update
    EXPECT_TRUE(strategy.update_watermarks());
    
    // Verify watermark file was updated
    EXPECT_TRUE(std::filesystem::exists(config_.watermark_file));
}

// Test CDC table creation and management
TEST_F(IncrementalLoadingStrategiesTest, DatabaseRecordChecker_CDCTableCreation) {
    auto checker = std::make_unique<omop::load::DatabaseRecordChecker>(mock_connector_);
    
    omop::load::CDCConfig cdc_config;
    cdc_config.cdc_type = omop::load::CDCConfig::CDCType::Timestamp;
    cdc_config.change_column = "updated_at";
    cdc_config.operation_column = "operation_type";
    cdc_config.track_deletes = true;
    cdc_config.create_cdc_table = true;
    
    // Mock the database connector responses
    EXPECT_CALL(*mock_connector_, table_exists(testing::_))
        .WillRepeatedly(testing::Return(false));
    
    EXPECT_CALL(*mock_connector_, execute_non_query(testing::_))
        .WillRepeatedly(testing::Return(true));
    
    // Test CDC table creation
    EXPECT_TRUE(checker->create_cdc_tracking_table("person", cdc_config));
}

// Test record change detection
TEST_F(IncrementalLoadingStrategiesTest, DeltaLoadingStrategy_ChangeDetection) {
    omop::load::DeltaLoadingStrategy strategy;
    ASSERT_TRUE(strategy.initialize(config_, context_));
    ASSERT_TRUE(strategy.begin_transaction(context_));
    
    // Create test records with UK NHS data
    auto record1 = create_uk_nhs_record(1001, "************", "M", "1985-03-15", "M1 1AA");
    auto record2 = create_uk_nhs_record(1002, "************", "F", "1992-07-22", "B1 2RT");
    auto record3 = create_uk_nhs_record(1001, "************", "M", "1985-03-15", "M1 1BB"); // Changed postcode
    
    // First record should be detected as new
    EXPECT_TRUE(strategy.check_record_changed(record1));
    
    // Second record should be detected as new
    EXPECT_TRUE(strategy.check_record_changed(record2));
    
    // Third record (same person_id but different data) should be detected as changed
    EXPECT_TRUE(strategy.check_record_changed(record3));
    
    EXPECT_TRUE(strategy.commit_transaction(context_));
}

// Test batch processing with incremental loading
TEST_F(IncrementalLoadingStrategiesTest, DeltaLoadingStrategy_BatchProcessing) {
    omop::load::DeltaLoadingStrategy strategy;
    ASSERT_TRUE(strategy.initialize(config_, context_));
    ASSERT_TRUE(strategy.begin_transaction(context_));
    
    // Create batch of UK NHS patient records
    omop::core::RecordBatch batch;
    batch.add_record(create_uk_nhs_record(2001, "************", "M", "1975-08-12", "SW1A 1AA"));
    batch.add_record(create_uk_nhs_record(2002, "************", "F", "1988-11-30", "E1 6AN"));
    batch.add_record(create_uk_nhs_record(2003, "************", "M", "1965-02-28", "WC2N 5DU"));
    batch.add_record(create_uk_nhs_record(2004, "************", "F", "1998-06-05", "SE1 9GF"));
    batch.add_record(create_uk_nhs_record(2005, "************", "M", "1982-12-18", "N1 9AG"));
    
    // Process batch
    size_t loaded_count = strategy.load_batch(batch, context_);
    
    // All records should be processed (new records)
    EXPECT_EQ(loaded_count, 5);
    
    auto metrics = strategy.get_metrics();
    EXPECT_EQ(metrics.total_records, 5);
    EXPECT_EQ(metrics.successful_records, 5);
    EXPECT_EQ(metrics.failed_records, 0);
    
    EXPECT_TRUE(strategy.commit_transaction(context_));
}

// Test watermark-based filtering
TEST_F(IncrementalLoadingStrategiesTest, DeltaLoadingStrategy_WatermarkFiltering) {
    omop::load::DeltaLoadingStrategy strategy;
    ASSERT_TRUE(strategy.initialize(config_, context_));
    
    // Set up watermark from 1 hour ago
    auto one_hour_ago = std::chrono::system_clock::now() - std::chrono::hours(1);
    create_watermark_file("person", one_hour_ago);
    
    ASSERT_TRUE(strategy.initialize_watermarks());
    ASSERT_TRUE(strategy.begin_transaction(context_));
    
    // Create records with different timestamps
    auto old_record = create_uk_nhs_record(3001, "************", "M", "1970-01-01", "M15 4FN");
    old_record.set_field("updated_at", one_hour_ago - std::chrono::minutes(30)); // Older than watermark
    
    auto new_record = create_uk_nhs_record(3002, "************", "F", "1985-05-20", "B2 4QA");
    new_record.set_field("updated_at", std::chrono::system_clock::now()); // Newer than watermark
    
    // Old record should not be processed (no change detected)
    // Note: This is simplified - real implementation would compare against database
    
    // New record should be processed
    EXPECT_TRUE(strategy.check_record_changed(new_record));
    
    EXPECT_TRUE(strategy.commit_transaction(context_));
}

// Test DatabaseRecordChecker functionality
TEST_F(IncrementalLoadingStrategiesTest, DatabaseRecordChecker_RecordOperations) {
    auto checker = std::make_unique<omop::load::DatabaseRecordChecker>(mock_connector_);
    
    // Mock database responses for record existence checking
    EXPECT_CALL(*mock_connector_, prepare_statement(testing::_))
        .WillRepeatedly(testing::Return(nullptr));
    
    auto test_record = create_uk_nhs_record(4001, "************", "M", "1990-03-15", "SW1A 1AA");
    
    // Test record existence check
    bool exists = checker->record_exists(test_record, "person", {"person_id"});
    // Note: With mocked connector, this will use the fallback simulation
    
    // Test change detection
    auto change_info = checker->check_record_changes(test_record, "person", 
                                                   {"person_id"}, {"birth_datetime", "gender_concept_id"});
    
    // Change type should be determined
    EXPECT_TRUE(change_info.change_type == omop::load::RecordChange::ChangeType::Insert ||
                change_info.change_type == omop::load::RecordChange::ChangeType::Update ||
                change_info.change_type == omop::load::RecordChange::ChangeType::None);
    
    // Test watermark retrieval
    auto max_watermark = checker->get_max_watermark("person", "updated_at");
    // Should return some value (even if mocked)
}

// Test CDC configuration and types
TEST_F(IncrementalLoadingStrategiesTest, CDCConfiguration_Types) {
    // Test different CDC types
    omop::load::CDCConfig timestamp_cdc;
    timestamp_cdc.cdc_type = omop::load::CDCConfig::CDCType::Timestamp;
    timestamp_cdc.change_column = "updated_at";
    timestamp_cdc.polling_interval = std::chrono::seconds(30);
    
    omop::load::CDCConfig sequence_cdc;
    sequence_cdc.cdc_type = omop::load::CDCConfig::CDCType::Sequence;
    sequence_cdc.change_column = "sequence_id";
    sequence_cdc.max_changes_per_batch = 5000;
    
    omop::load::CDCConfig hash_cdc;
    hash_cdc.cdc_type = omop::load::CDCConfig::CDCType::Hash;
    hash_cdc.operation_column = "operation_type";
    hash_cdc.track_deletes = false;
    
    omop::load::CDCConfig hybrid_cdc;
    hybrid_cdc.cdc_type = omop::load::CDCConfig::CDCType::Hybrid;
    hybrid_cdc.create_cdc_table = true;
    
    // Verify configuration properties
    EXPECT_EQ(timestamp_cdc.change_column, "updated_at");
    EXPECT_EQ(sequence_cdc.max_changes_per_batch, 5000);
    EXPECT_FALSE(hash_cdc.track_deletes);
    EXPECT_TRUE(hybrid_cdc.create_cdc_table);
}

// Test watermark information and UK datetime formats
TEST_F(IncrementalLoadingStrategiesTest, WatermarkInfo_UKDateTimeFormats) {
    omop::load::WatermarkInfo watermark;
    watermark.type = omop::load::WatermarkInfo::WatermarkType::Timestamp;
    watermark.column_name = "last_updated";
    watermark.use_uk_datetime_format = true;
    watermark.uk_date_format = "%d/%m/%Y %H:%M:%S";
    watermark.persistence_path = (watermark_dir_ / "test_watermark.json").string();
    
    // Test UK datetime format
    auto now = std::chrono::system_clock::now();
    watermark.current_value = now;
    watermark.last_updated = now;
    
    // Verify watermark properties
    EXPECT_EQ(watermark.column_name, "last_updated");
    EXPECT_TRUE(watermark.use_uk_datetime_format);
    EXPECT_EQ(watermark.uk_date_format, "%d/%m/%Y %H:%M:%S");
    EXPECT_EQ(watermark.type, omop::load::WatermarkInfo::WatermarkType::Timestamp);
    
    // Test composite watermark
    omop::load::WatermarkInfo composite_watermark;
    composite_watermark.type = omop::load::WatermarkInfo::WatermarkType::Composite;
    composite_watermark.composite_columns = {"updated_date", "sequence_id"};
    
    EXPECT_EQ(composite_watermark.composite_columns.size(), 2);
    EXPECT_EQ(composite_watermark.composite_columns[0], "updated_date");
    EXPECT_EQ(composite_watermark.composite_columns[1], "sequence_id");
}

// Test loading strategy factory with new strategies
TEST_F(IncrementalLoadingStrategiesTest, LoadingStrategyFactory_DeltaStrategy) {
    // Test factory creation of delta loading strategy
    auto strategy = omop::load::LoadingStrategyFactory::create_strategy(
        omop::load::LoadingStrategy::DeltaLoad);
    
    ASSERT_NE(strategy, nullptr);
    EXPECT_EQ(strategy->get_strategy_type(), omop::load::LoadingStrategy::DeltaLoad);
    
    // Test available strategies includes delta
    auto available = omop::load::LoadingStrategyFactory::get_available_strategies();
    EXPECT_TRUE(std::find(available.begin(), available.end(), 
                         omop::load::LoadingStrategy::DeltaLoad) != available.end());
    
    // Test recommended strategy for large datasets
    auto recommended = omop::load::LoadingStrategyFactory::get_recommended_strategy(
        100000, "postgresql");
    
    // For large datasets, should recommend efficient strategies
    EXPECT_TRUE(recommended == omop::load::LoadingStrategy::BulkLoad ||
                recommended == omop::load::LoadingStrategy::ParallelLoad ||
                recommended == omop::load::LoadingStrategy::DeltaLoad);
}

// Test strategy utilities with new loading modes
TEST_F(IncrementalLoadingStrategiesTest, LoadingStrategyUtils_StringConversion) {
    // Test strategy to string conversion
    EXPECT_EQ(omop::load::LoadingStrategyUtils::strategy_to_string(
                omop::load::LoadingStrategy::DeltaLoad), "DeltaLoad");
    
    // Test string to strategy conversion
    EXPECT_EQ(omop::load::LoadingStrategyUtils::string_to_strategy("DeltaLoad"), 
              omop::load::LoadingStrategy::DeltaLoad);
    EXPECT_EQ(omop::load::LoadingStrategyUtils::string_to_strategy("deltaload"), 
              omop::load::LoadingStrategy::DeltaLoad);
    
    // Test mode conversions
    EXPECT_EQ(omop::load::LoadingStrategyUtils::mode_to_string(
                omop::load::LoadingMode::Merge), "Merge");
    EXPECT_EQ(omop::load::LoadingStrategyUtils::string_to_mode("merge"), 
              omop::load::LoadingMode::Merge);
    
    // Test default configuration for delta strategy
    auto default_config = omop::load::LoadingStrategyUtils::get_default_config(
        omop::load::LoadingStrategy::DeltaLoad);
    
    EXPECT_EQ(default_config.strategy, omop::load::LoadingStrategy::DeltaLoad);
    EXPECT_GT(default_config.batch_size, 0);
}

// Test error handling and validation
TEST_F(IncrementalLoadingStrategiesTest, DeltaLoadingStrategy_ErrorHandling) {
    omop::load::DeltaLoadingStrategy strategy;
    
    // Test invalid configuration
    omop::load::LoadingConfig invalid_config = config_;
    invalid_config.target_table = ""; // Invalid empty table name
    
    auto validation_errors = strategy.validate_config(invalid_config);
    EXPECT_FALSE(validation_errors.empty()); // Should have validation errors
    
    // Test proper error handling during transaction
    ASSERT_TRUE(strategy.initialize(config_, context_));
    
    // Try to load without transaction
    auto test_record = create_uk_nhs_record(5001, "************", "M", "1995-01-01", "EC1A 1BB");
    EXPECT_FALSE(strategy.load_record(test_record, context_)); // Should fail without transaction
    
    // Test rollback functionality
    ASSERT_TRUE(strategy.begin_transaction(context_));
    EXPECT_TRUE(strategy.rollback_transaction(context_));
}

// Integration test with multiple loading strategies
TEST_F(IncrementalLoadingStrategiesTest, LoadingStrategy_Integration) {
    // Test switching between strategies
    auto batch_strategy = omop::load::LoadingStrategyFactory::create_strategy(
        omop::load::LoadingStrategy::BatchInsert);
    auto delta_strategy = omop::load::LoadingStrategyFactory::create_strategy(
        omop::load::LoadingStrategy::DeltaLoad);
    auto upsert_strategy = omop::load::LoadingStrategyFactory::create_strategy(
        omop::load::LoadingStrategy::UpsertLoad);
    
    ASSERT_NE(batch_strategy, nullptr);
    ASSERT_NE(delta_strategy, nullptr);
    ASSERT_NE(upsert_strategy, nullptr);
    
    // Initialize all strategies
    EXPECT_TRUE(batch_strategy->initialize(config_, context_));
    EXPECT_TRUE(delta_strategy->initialize(config_, context_));
    EXPECT_TRUE(upsert_strategy->initialize(config_, context_));
    
    // Test different supported modes
    auto batch_modes = batch_strategy->get_supported_modes();
    auto delta_modes = delta_strategy->get_supported_modes();
    auto upsert_modes = upsert_strategy->get_supported_modes();
    
    EXPECT_FALSE(batch_modes.empty());
    EXPECT_FALSE(delta_modes.empty());
    EXPECT_FALSE(upsert_modes.empty());
    
    // Delta strategy should support upsert mode
    EXPECT_TRUE(std::find(delta_modes.begin(), delta_modes.end(), 
                         omop::load::LoadingMode::Upsert) != delta_modes.end());
}

} // namespace omop::test::load