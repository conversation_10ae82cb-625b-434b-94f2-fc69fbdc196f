# Common integration tests
set(COMMON_INTEGRATION_TEST_SOURCES
    config_file_management_test.cpp
    string_file_utilities_test.cpp
    validation_rules_engine_test.cpp
)

add_executable(common_integration_tests ${COMMON_INTEGRATION_TEST_SOURCES})

target_link_libraries(common_integration_tests
    PRIVATE
        omop_common
        omop_core
        omop_cdm
        omop_extract
        omop_transform
        omop_load
#        omop_service
        integration_test_helpers
        gtest
        gtest_main
        gmock
)

target_include_directories(common_integration_tests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/src/lib
        ${CMAKE_SOURCE_DIR}/tests/integration
)

add_test(
    NAME common_integration_tests
    COMMAND common_integration_tests
)

set_tests_properties(common_integration_tests PROPERTIES
    TIMEOUT 300
    LABELS "integration;common"
    ENVIRONMENT "TEST_DATA_DIR=${TEST_DATA_DIR}"
)