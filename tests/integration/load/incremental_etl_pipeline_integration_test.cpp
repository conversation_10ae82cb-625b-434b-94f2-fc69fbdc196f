#include <gtest/gtest.h>
#include "core/pipeline.h"
#include "load/loader_strategies.h"
#include "extract/csv_extractor.h"
#include "transform/transformation_engine.h"
#include "common/configuration.h"
#include "test_helpers/database_fixture.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

namespace omop::test::integration::load {

/**
 * @brief Integration test for incremental ETL pipeline
 *
 * Tests end-to-end incremental processing with CDC, watermarking, and delta loading
 * using UK NHS healthcare data scenarios.
 */
class IncrementalETLPipelineTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();
        
        // Create test directories
        test_data_dir_ = std::filesystem::temp_directory_path() / "incremental_etl_test";
        watermark_dir_ = test_data_dir_ / "watermarks";
        std::filesystem::create_directories(watermark_dir_);
        
        // Initialize configuration manager
        config_manager_ = std::make_unique<omop::common::ConfigurationManager>();
        
        // Create incremental configuration
        create_incremental_config();
        
        // Create initial test data
        create_initial_test_data();
        
        // Set up pipeline manager
        pipeline_manager_ = std::make_shared<omop::core::PipelineManager>(2);
    }
    
    void TearDown() override {
        if (std::filesystem::exists(test_data_dir_)) {
            std::filesystem::remove_all(test_data_dir_);
        }
        DatabaseFixture::TearDown();
    }
    
    void create_incremental_config() {
        std::string config_yaml = std::format(R"(
version: "2.0"

# Database connections
source_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "uk_healthcare_source"
  username: "test_user"
  password: "test_pass"

target_database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "omop_cdm_test"
  username: "test_user"
  password: "test_pass"

# Global incremental loading configuration
loading:
  strategy: "delta_load"
  mode: "upsert"
  batch_size: 100
  parallel_workers: 2
  enable_cdc: true
  enable_hash_comparison: true
  incremental_processing: true
  default_watermark_directory: "{}"
  
  # CDC settings
  cdc:
    type: "timestamp"
    change_column: "updated_at"
    operation_column: "operation_type"
    track_deletes: true
    create_cdc_table: true
    polling_interval: 30
    max_changes_per_batch: 1000
  
  # Global watermark settings
  watermark:
    type: "timestamp"
    column_name: "last_updated"
    use_uk_datetime_format: true
    uk_date_format: "%d/%m/%Y %H:%M:%S"
  
  # Table-specific configurations
  tables:
    person:
      strategy: "delta_load"
      mode: "upsert"
      enable_cdc: true
      watermark_column: "updated_datetime"
      watermark_file: "{}/person.watermark"
      tracking_columns: ["birth_datetime", "gender_concept_id", "race_concept_id"]
      cdc:
        type: "hybrid"
        change_column: "last_modified"
    
    visit_occurrence:
      strategy: "upsert_load"
      mode: "merge"
      enable_cdc: true
      watermark_column: "visit_end_datetime"
      watermark_file: "{}/visit_occurrence.watermark"
      tracking_columns: ["visit_start_datetime", "visit_end_datetime"]
      cdc:
        type: "sequence"
        change_column: "sequence_id"

# ETL settings
etl_settings:
  batch_size: 100
  parallel_workers: 2
  validation_mode: "strict"
  error_threshold: 0.05

# Table mappings for UK NHS data
tables:
  person:
    source_table: "uk_patients"
    target_table: "person"
    transformations:
      - source_column: "patient_id"
        target_column: "person_id"
        type: "direct"
      - source_column: "nhs_number"
        target_column: "person_source_value"
        type: "direct"
      - source_columns: ["first_name", "last_name"]
        target_column: "person_source_value_2"
        type: "string_concatenation"
        parameters:
          separator: " "
      - source_column: "date_of_birth"
        target_column: "birth_datetime"
        type: "date_transform"
        parameters:
          input_format: "%d/%m/%Y"
          output_format: "%Y-%m-%d 00:00:00"
      - source_column: "gender"
        target_column: "gender_concept_id"
        type: "vocabulary_mapping"
        parameters:
          vocabulary_id: "Gender"
          default_value: 0
  
  visit_occurrence:
    source_table: "uk_visits"
    target_table: "visit_occurrence"
    transformations:
      - source_column: "visit_id"
        target_column: "visit_occurrence_id"
        type: "direct"
      - source_column: "patient_id"
        target_column: "person_id"
        type: "direct"
      - source_column: "admission_date"
        target_column: "visit_start_datetime"
        type: "date_transform"
        parameters:
          input_format: "%d/%m/%Y %H:%M"
          output_format: "%Y-%m-%d %H:%M:%S"
      - source_column: "discharge_date"
        target_column: "visit_end_datetime"
        type: "date_transform"
        parameters:
          input_format: "%d/%m/%Y %H:%M"
          output_format: "%Y-%m-%d %H:%M:%S"
)", watermark_dir_.string(), watermark_dir_.string(), watermark_dir_.string());
        
        config_manager_->load_config_from_string(config_yaml);
    }
    
    void create_initial_test_data() {
        // Create initial UK patients data
        create_uk_patients_csv("uk_patients_initial.csv", 0);
        
        // Create initial visits data
        create_uk_visits_csv("uk_visits_initial.csv", 0);
        
        // Create incremental update files
        create_uk_patients_csv("uk_patients_update1.csv", 1);
        create_uk_patients_csv("uk_patients_update2.csv", 2);
        
        create_uk_visits_csv("uk_visits_update1.csv", 1);
        create_uk_visits_csv("uk_visits_update2.csv", 2);
    }
    
    void create_uk_patients_csv(const std::string& filename, int update_batch) {
        std::filesystem::path file_path = test_data_dir_ / filename;
        std::ofstream file(file_path);
        
        // CSV header
        file << "patient_id,nhs_number,first_name,last_name,date_of_birth,gender,postcode,updated_datetime,last_modified\\n";
        
        auto base_time = std::chrono::system_clock::now() - std::chrono::hours(24 - update_batch);
        auto time_t = std::chrono::system_clock::to_time_t(base_time);
        std::tm* tm = std::localtime(&time_t);
        char timestamp[20];
        std::strftime(timestamp, sizeof(timestamp), "%d/%m/%Y %H:%M:%S", tm);
        
        int base_id = update_batch * 1000;
        
        // Generate UK NHS test data
        std::vector<std::tuple<std::string, std::string, std::string, std::string, std::string, std::string>> patients;
        
        if (update_batch == 0) {
            // Initial load
            patients = {
                {"John", "Smith", "01/01/1980", "M", "SW1A 1AA", "************"},
                {"Sarah", "Johnson", "15/05/1985", "F", "M1 1AA", "************"},
                {"Michael", "Brown", "22/09/1975", "M", "B1 2RT", "************"},
                {"Emma", "Davis", "03/12/1990", "F", "E1 6AN", "************"},
                {"James", "Wilson", "18/07/1988", "M", "WC2N 5DU", "************"}
            };
        } else if (update_batch == 1) {
            // First update - some new patients, some address changes
            patients = {
                {"John", "Smith", "01/01/1980", "M", "SW1A 1BB", "************"}, // Address change
                {"Oliver", "Taylor", "28/02/1992", "M", "N1 9AG", "************"}, // New patient
                {"Sophie", "Anderson", "11/11/1987", "F", "SE1 9GF", "************"} // New patient
            };
        } else if (update_batch == 2) {
            // Second update - more changes
            patients = {
                {"Sarah", "Johnson", "15/05/1985", "F", "M1 1BB", "************"}, // Address change
                {"Emma", "Davis", "03/12/1990", "F", "E2 7AN", "************"}, // Address change
                {"Lucas", "Martin", "05/06/1983", "M", "EC1A 1BB", "************"} // New patient
            };
        }
        
        for (size_t i = 0; i < patients.size(); ++i) {
            const auto& [first_name, last_name, dob, gender, postcode, nhs_number] = patients[i];
            int patient_id = base_id + static_cast<int>(i) + 1;
            
            file << patient_id << ","
                 << nhs_number << ","
                 << first_name << ","
                 << last_name << ","
                 << dob << ","
                 << gender << ","
                 << postcode << ","
                 << timestamp << ","
                 << timestamp << "\\n";
        }
        
        file.close();
    }
    
    void create_uk_visits_csv(const std::string& filename, int update_batch) {
        std::filesystem::path file_path = test_data_dir_ / filename;
        std::ofstream file(file_path);
        
        // CSV header
        file << "visit_id,patient_id,admission_date,discharge_date,visit_type,sequence_id,visit_end_datetime\\n";
        
        auto base_time = std::chrono::system_clock::now() - std::chrono::hours(24 - update_batch);
        auto time_t = std::chrono::system_clock::to_time_t(base_time);
        std::tm* tm = std::localtime(&time_t);
        
        char admission_date[20], discharge_date[20];
        std::strftime(admission_date, sizeof(admission_date), "%d/%m/%Y %H:%M", tm);
        
        // Discharge 2 hours later
        auto discharge_time = base_time + std::chrono::hours(2);
        auto discharge_time_t = std::chrono::system_clock::to_time_t(discharge_time);
        std::tm* discharge_tm = std::localtime(&discharge_time_t);
        std::strftime(discharge_date, sizeof(discharge_date), "%d/%m/%Y %H:%M", discharge_tm);
        
        int base_visit_id = update_batch * 10000;
        int base_patient_id = update_batch * 1000;
        int base_sequence = update_batch * 100;
        
        // Generate visit data
        for (int i = 0; i < 3; ++i) {
            int visit_id = base_visit_id + i + 1;
            int patient_id = base_patient_id + i + 1;
            int sequence_id = base_sequence + i + 1;
            
            file << visit_id << ","
                 << patient_id << ","
                 << admission_date << ","
                 << discharge_date << ","
                 << "Outpatient,"
                 << sequence_id << ","
                 << discharge_date << "\\n";
        }
        
        file.close();
    }
    
    // Helper method to run incremental pipeline
    std::pair<size_t, size_t> run_incremental_pipeline(const std::string& patients_file, 
                                                        const std::string& visits_file,
                                                        const std::string& job_name_suffix = "") {
        // Create pipeline configuration
        omop::core::ETLPipelineConfig pipeline_config;
        pipeline_config.pipeline_id = "incremental_test" + job_name_suffix;
        pipeline_config.pipeline_name = "Incremental ETL Test" + job_name_suffix;
        pipeline_config.execution_mode = omop::core::ExecutionMode::Sequential;
        pipeline_config.batch_size = 50;
        pipeline_config.enable_checkpointing = true;
        pipeline_config.checkpoint_directory = (test_data_dir_ / "checkpoints").string();
        
        // Create pipeline
        auto pipeline = std::make_unique<omop::core::ETLPipeline>();
        pipeline->initialize(pipeline_config);
        
        // Set up extractors, transformers, and loaders for person table
        auto person_extractor = std::make_unique<omop::extract::CsvExtractor>();
        std::unordered_map<std::string, std::any> person_extract_config;
        person_extract_config["filepath"] = (test_data_dir_ / patients_file).string();
        person_extract_config["has_header"] = true;
        person_extract_config["delimiter"] = ",";
        
        omop::core::ProcessingContext person_context;
        person_extractor->initialize(person_extract_config, person_context);
        
        // Set up transformation engine for person
        auto person_transformer = std::make_unique<omop::transform::TransformationEngine>();
        auto person_table_mapping = config_manager_->get_table_mapping("person");
        EXPECT_TRUE(person_table_mapping.has_value());
        
        std::unordered_map<std::string, std::any> person_transform_config;
        person_transform_config["target_table"] = "person";
        person_transformer->initialize(person_transform_config, person_context);
        
        // Set up incremental loader for person
        auto person_loading_config = config_manager_->get_table_loading_config("person");
        EXPECT_TRUE(person_loading_config.has_value());
        
        auto person_strategy = omop::load::LoadingStrategyFactory::create_strategy(
            person_loading_config->get_strategy());
        EXPECT_NE(person_strategy, nullptr);
        
        auto person_loading_struct = person_loading_config->to_loading_config();
        person_loading_struct.target_table = "person";
        
        EXPECT_TRUE(person_strategy->initialize(person_loading_struct, person_context));
        
        // Process person data
        size_t person_records_processed = 0;
        EXPECT_TRUE(person_strategy->begin_transaction(person_context));
        
        while (person_extractor->has_more_data(person_context)) {
            auto batch = person_extractor->extract_batch(person_context);
            if (!batch.empty()) {
                auto transformed_batch = person_transformer->transform_batch(batch, person_context);
                person_records_processed += person_strategy->load_batch(transformed_batch, person_context);
            }
        }
        
        EXPECT_TRUE(person_strategy->commit_transaction(person_context));
        EXPECT_TRUE(person_strategy->finalize(person_context));
        
        // Process visits data similarly
        size_t visit_records_processed = 0;
        if (!visits_file.empty()) {
            auto visit_extractor = std::make_unique<omop::extract::CsvExtractor>();
            std::unordered_map<std::string, std::any> visit_extract_config;
            visit_extract_config["filepath"] = (test_data_dir_ / visits_file).string();
            visit_extract_config["has_header"] = true;
            visit_extract_config["delimiter"] = ",";
            
            omop::core::ProcessingContext visit_context;
            visit_extractor->initialize(visit_extract_config, visit_context);
            
            auto visit_transformer = std::make_unique<omop::transform::TransformationEngine>();
            auto visit_table_mapping = config_manager_->get_table_mapping("visit_occurrence");
            EXPECT_TRUE(visit_table_mapping.has_value());
            
            std::unordered_map<std::string, std::any> visit_transform_config;
            visit_transform_config["target_table"] = "visit_occurrence";
            visit_transformer->initialize(visit_transform_config, visit_context);
            
            auto visit_loading_config = config_manager_->get_table_loading_config("visit_occurrence");
            EXPECT_TRUE(visit_loading_config.has_value());
            
            auto visit_strategy = omop::load::LoadingStrategyFactory::create_strategy(
                visit_loading_config->get_strategy());
            EXPECT_NE(visit_strategy, nullptr);
            
            auto visit_loading_struct = visit_loading_config->to_loading_config();
            visit_loading_struct.target_table = "visit_occurrence";
            
            EXPECT_TRUE(visit_strategy->initialize(visit_loading_struct, visit_context));
            EXPECT_TRUE(visit_strategy->begin_transaction(visit_context));
            
            while (visit_extractor->has_more_data(visit_context)) {
                auto batch = visit_extractor->extract_batch(visit_context);
                if (!batch.empty()) {
                    auto transformed_batch = visit_transformer->transform_batch(batch, visit_context);
                    visit_records_processed += visit_strategy->load_batch(transformed_batch, visit_context);
                }
            }
            
            EXPECT_TRUE(visit_strategy->commit_transaction(visit_context));
            EXPECT_TRUE(visit_strategy->finalize(visit_context));
        }
        
        return {person_records_processed, visit_records_processed};
    }

protected:
    std::filesystem::path test_data_dir_;
    std::filesystem::path watermark_dir_;
    std::unique_ptr<omop::common::ConfigurationManager> config_manager_;
    std::shared_ptr<omop::core::PipelineManager> pipeline_manager_;
};

// Test initial incremental load
TEST_F(IncrementalETLPipelineTest, InitialIncrementalLoad) {
    // Run initial load
    auto [person_count, visit_count] = run_incremental_pipeline(
        "uk_patients_initial.csv", "uk_visits_initial.csv", "_initial");
    
    // Verify initial load processed all records
    EXPECT_EQ(person_count, 5); // 5 initial patients
    EXPECT_EQ(visit_count, 3);  // 3 initial visits
    
    // Verify watermark files were created
    EXPECT_TRUE(std::filesystem::exists(watermark_dir_ / "person.watermark"));
    EXPECT_TRUE(std::filesystem::exists(watermark_dir_ / "visit_occurrence.watermark"));
    
    // Verify watermark file contents
    std::ifstream person_watermark(watermark_dir_ / "person.watermark");
    EXPECT_TRUE(person_watermark.is_open());
    
    std::string watermark_content;
    person_watermark >> watermark_content;
    EXPECT_FALSE(watermark_content.empty());
}

// Test incremental updates with change detection
TEST_F(IncrementalETLPipelineTest, IncrementalUpdatesWithChangeDetection) {
    // Run initial load first
    auto [initial_person_count, initial_visit_count] = run_incremental_pipeline(
        "uk_patients_initial.csv", "uk_visits_initial.csv", "_initial");
    
    EXPECT_EQ(initial_person_count, 5);
    EXPECT_EQ(initial_visit_count, 3);
    
    // Wait a moment to ensure different timestamps
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Run first incremental update
    auto [update1_person_count, update1_visit_count] = run_incremental_pipeline(
        "uk_patients_update1.csv", "uk_visits_update1.csv", "_update1");
    
    // Should process changed and new records
    EXPECT_GT(update1_person_count, 0); // Should have processed some records
    EXPECT_GT(update1_visit_count, 0);
    
    // Wait and run second incremental update
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    auto [update2_person_count, update2_visit_count] = run_incremental_pipeline(
        "uk_patients_update2.csv", "uk_visits_update2.csv", "_update2");
    
    EXPECT_GT(update2_person_count, 0);
    EXPECT_GT(update2_visit_count, 0);
}

// Test watermark-based filtering
TEST_F(IncrementalETLPipelineTest, WatermarkBasedFiltering) {
    // Create watermark files with specific timestamps
    auto one_hour_ago = std::chrono::system_clock::now() - std::chrono::hours(1);
    
    // Create person watermark
    std::filesystem::path person_watermark_path = watermark_dir_ / "person.watermark";
    std::ofstream person_watermark_file(person_watermark_path);
    
    nlohmann::json person_watermark_json;
    person_watermark_json["table"] = "person";
    person_watermark_json["timestamp"] = std::chrono::system_clock::to_time_t(one_hour_ago);
    person_watermark_json["last_updated"] = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
    
    person_watermark_file << person_watermark_json.dump(2);
    person_watermark_file.close();
    
    // Run incremental load - should process only newer records
    auto [person_count, visit_count] = run_incremental_pipeline(
        "uk_patients_initial.csv", "uk_visits_initial.csv", "_watermark_test");
    
    // Records should be processed based on watermark comparison
    EXPECT_GE(person_count, 0); // May process all or none depending on timestamps
    
    // Verify watermark was updated
    EXPECT_TRUE(std::filesystem::exists(person_watermark_path));
    
    std::ifstream updated_watermark(person_watermark_path);
    std::string updated_content;
    updated_watermark.seekg(0, std::ios::end);
    updated_content.reserve(updated_watermark.tellg());
    updated_watermark.seekg(0, std::ios::beg);
    updated_content.assign(std::istreambuf_iterator<char>(updated_watermark),
                          std::istreambuf_iterator<char>());
    
    EXPECT_FALSE(updated_content.empty());
}

// Test CDC table creation and management
TEST_F(IncrementalETLPipelineTest, CDCTableCreationAndManagement) {
    // Test that CDC mechanisms work with the pipeline
    auto person_loading_config = config_manager_->get_table_loading_config("person");
    ASSERT_TRUE(person_loading_config.has_value());
    EXPECT_TRUE(person_loading_config->is_cdc_enabled());
    
    const auto& cdc_config = person_loading_config->get_cdc_config();
    EXPECT_EQ(cdc_config.cdc_type, omop::load::CDCConfig::CDCType::Hybrid);
    EXPECT_EQ(cdc_config.change_column, "last_modified");
    
    // Run pipeline with CDC enabled
    auto [person_count, visit_count] = run_incremental_pipeline(
        "uk_patients_initial.csv", "uk_visits_initial.csv", "_cdc_test");
    
    EXPECT_GT(person_count, 0);
    
    // Verify CDC-related processing occurred
    // Note: In a real implementation, this would check for CDC table creation
    // and change tracking records
}

// Test different loading strategies in incremental pipeline
TEST_F(IncrementalETLPipelineTest, DifferentLoadingStrategies) {
    // Test that person uses delta_load strategy
    auto person_config = config_manager_->get_table_loading_config("person");
    ASSERT_TRUE(person_config.has_value());
    EXPECT_EQ(person_config->get_strategy(), omop::load::LoadingStrategy::DeltaLoad);
    
    // Test that visit_occurrence uses upsert_load strategy
    auto visit_config = config_manager_->get_table_loading_config("visit_occurrence");
    ASSERT_TRUE(visit_config.has_value());
    EXPECT_EQ(visit_config->get_strategy(), omop::load::LoadingStrategy::UpsertLoad);
    
    // Run pipeline to test both strategies
    auto [person_count, visit_count] = run_incremental_pipeline(
        "uk_patients_initial.csv", "uk_visits_initial.csv", "_mixed_strategies");
    
    EXPECT_GT(person_count, 0);
    EXPECT_GT(visit_count, 0);
}

// Test error handling in incremental pipeline
TEST_F(IncrementalETLPipelineTest, ErrorHandlingInIncrementalPipeline) {
    // Create invalid data file
    std::filesystem::path invalid_file = test_data_dir_ / "invalid_patients.csv";
    std::ofstream file(invalid_file);
    file << "patient_id,invalid_header\\n";
    file << "invalid_data,more_invalid_data\\n";
    file.close();
    
    // Attempt to run pipeline with invalid data
    // Should handle errors gracefully without crashing
    EXPECT_NO_THROW({
        auto [person_count, visit_count] = run_incremental_pipeline(
            "invalid_patients.csv", "", "_error_test");
        // May process 0 records due to errors, but should not crash
    });
}

// Test parallel incremental processing
TEST_F(IncrementalETLPipelineTest, ParallelIncrementalProcessing) {
    // Test concurrent incremental jobs
    std::vector<std::future<std::pair<size_t, size_t>>> futures;
    
    // Submit multiple jobs concurrently
    for (int i = 0; i < 2; ++i) {
        futures.push_back(std::async(std::launch::async, [this, i]() {
            return run_incremental_pipeline(
                "uk_patients_initial.csv", 
                "uk_visits_initial.csv", 
                "_parallel_" + std::to_string(i));
        }));
    }
    
    // Wait for all jobs to complete
    size_t total_person_records = 0;
    size_t total_visit_records = 0;
    
    for (auto& future : futures) {
        auto [person_count, visit_count] = future.get();
        total_person_records += person_count;
        total_visit_records += visit_count;
    }
    
    // Verify both jobs processed records
    EXPECT_GT(total_person_records, 0);
    EXPECT_GT(total_visit_records, 0);
}

// Test UK-specific data handling in incremental pipeline
TEST_F(IncrementalETLPipelineTest, UKSpecificDataHandling) {
    // Verify UK-specific configuration is working
    const auto& loading_config = config_manager_->get_loading_config();
    const auto& watermark_info = loading_config.get_watermark_info();
    
    EXPECT_TRUE(watermark_info.use_uk_datetime_format);
    EXPECT_EQ(watermark_info.uk_date_format, "%d/%m/%Y %H:%M:%S");
    
    // Run pipeline with UK data
    auto [person_count, visit_count] = run_incremental_pipeline(
        "uk_patients_initial.csv", "uk_visits_initial.csv", "_uk_data_test");
    
    EXPECT_GT(person_count, 0);
    EXPECT_GT(visit_count, 0);
    
    // Verify NHS numbers and UK postcodes are processed
    // In a full implementation, this would validate the transformed data
    // contains properly formatted NHS numbers and UK postcodes
}

// Test complete incremental workflow with multiple updates
TEST_F(IncrementalETLPipelineTest, CompleteIncrementalWorkflow) {
    // Step 1: Initial load
    auto [initial_person, initial_visit] = run_incremental_pipeline(
        "uk_patients_initial.csv", "uk_visits_initial.csv", "_workflow_initial");
    
    EXPECT_EQ(initial_person, 5);
    EXPECT_EQ(initial_visit, 3);
    
    // Step 2: First incremental update
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    auto [update1_person, update1_visit] = run_incremental_pipeline(
        "uk_patients_update1.csv", "uk_visits_update1.csv", "_workflow_update1");
    
    EXPECT_GT(update1_person, 0);
    EXPECT_GT(update1_visit, 0);
    
    // Step 3: Second incremental update
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    auto [update2_person, update2_visit] = run_incremental_pipeline(
        "uk_patients_update2.csv", "uk_visits_update2.csv", "_workflow_update2");
    
    EXPECT_GT(update2_person, 0);
    EXPECT_GT(update2_visit, 0);
    
    // Verify watermark files have been updated throughout
    EXPECT_TRUE(std::filesystem::exists(watermark_dir_ / "person.watermark"));
    EXPECT_TRUE(std::filesystem::exists(watermark_dir_ / "visit_occurrence.watermark"));
    
    // Verify watermark files contain valid JSON with recent timestamps
    std::ifstream person_watermark(watermark_dir_ / "person.watermark");
    nlohmann::json watermark_json;
    person_watermark >> watermark_json;
    
    EXPECT_TRUE(watermark_json.contains("table"));
    EXPECT_TRUE(watermark_json.contains("timestamp"));
    EXPECT_TRUE(watermark_json.contains("last_updated"));
    EXPECT_EQ(watermark_json["table"], "person");
}

} // namespace omop::test::integration::load