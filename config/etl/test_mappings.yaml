# OMOP ETL Test Configuration
# This configuration is designed for end-to-end testing scenarios

metadata:
  name: "OMOP ETL Test Configuration"
  version: "1.0.0"
  description: "Test configuration for end-to-end ETL pipeline testing"
  author: "UCL OMOP ETL Team"

# Source database configuration (MySQL test database)
source:
  type: mysql
  connection:
    host: ${MYSQL_HOST:-localhost}
    port: ${MYSQL_PORT:-3306}
    database: ${MYSQL_DB:-clinical_db}
    username: ${MYSQL_USER:-clinical_user}
    password: ${MYSQL_PASSWORD:-clinical_password}
    pool_size: 5
    connection_timeout: 30
    charset: utf8mb4
    
  validation_query: "SELECT 1"
  test_on_borrow: true

# Target OMOP CDM database (PostgreSQL)
target:
  type: postgresql
  connection:
    host: ${OMOP_HOST:-localhost}
    port: ${OMOP_PORT:-5432}
    database: ${OMOP_DB:-omop_cdm}
    username: ${OMOP_USER:-omop_user}
    password: ${OMOP_PASSWORD:-omop_password}
    schema: cdm
    pool_size: 5
    connection_timeout: 60
    
  batch_insert_size: 1000
  use_bulk_insert: true

# Job configuration optimized for testing
job_config:
  batch_size: 1000
  parallel_threads: 2
  max_concurrent_jobs: 2
  enable_checkpointing: true
  checkpoint_interval: 1000
  enable_validation: true
  validation_threshold: 0.90
  max_error_rate: 0.10
  retry_attempts: 2
  retry_delay_seconds: 30

# Logging configuration for testing
logging:
  level: DEBUG
  format: json
  include_sql: true
  log_validation_errors: true
  log_transformation_details: true

# Performance monitoring
monitoring:
  enable_metrics: true
  metrics_interval: 10
  enable_profiling: true
  memory_threshold_mb: 1024

# Security settings for testing
security:
  enable_encryption: false
  anonymization_salt: "test_salt_12345"
  enable_audit_logging: true
  mask_sensitive_data_in_logs: false

# Table mappings for testing
mappings:
  person:
    source_query: |
      SELECT 
        p.patient_id,
        p.first_name,
        p.last_name,
        p.birth_date,
        p.gender,
        p.race_code,
        p.ethnicity_code,
        p.zip_code,
        p.created_at,
        p.updated_at
      FROM patients p
      WHERE p.active = 1
        AND p.deleted_at IS NULL
    
    target_table: person
    
    transformations:
      - source_column: patient_id
        target_column: person_id
        type: direct
        validation:
          required: true
          data_type: integer
          min_value: 1
      
      - source_column: birth_date
        target_column: birth_datetime
        type: date_transform
        input_format: "%Y-%m-%d"
        output_format: "%Y-%m-%d 00:00:00"
        validation:
          required: true
          min_date: "1900-01-01"
          max_date: "2024-12-31"
      
      - source_column: gender
        target_column: gender_concept_id
        type: vocabulary_mapping
        vocabulary: Gender
        mapping:
          "M": 8507      # Male
          "F": 8532      # Female
          "U": 0         # Unknown
        default_value: 0
        validation:
          required: true
          valid_concept_ids: [8507, 8532, 0]
      
      - source_column: race_code
        target_column: race_concept_id
        type: vocabulary_mapping
        vocabulary: Race
        mapping:
          "1": 8527      # White
          "2": 8516      # Black
          "3": 8515      # Asian
          "4": 8557      # Native American
          "5": 8522      # Other
        default_value: 0
        validation:
          valid_concept_ids: [8527, 8516, 8515, 8557, 8522, 0]
      
      # Static values for required OMOP fields
      - target_column: year_of_birth
        type: calculated
        expression: "YEAR(birth_datetime)"
        validation:
          required: true
          data_type: integer
      
      - target_column: month_of_birth
        type: calculated
        expression: "MONTH(birth_datetime)"
        validation:
          data_type: integer
      
      - target_column: day_of_birth
        type: calculated
        expression: "DAY(birth_datetime)"
        validation:
          data_type: integer

    validation_rules:
      - field: person_id
        type: required
        error_message: "Person ID is required"
      
      - field: birth_datetime
        type: date_range
        min_date: "1900-01-01"
        max_date: "2024-12-31"
        error_message: "Birth date must be between 1900 and 2024"

  visit_occurrence:
    source_query: |
      SELECT 
        e.encounter_id,
        e.patient_id,
        e.visit_start_date,
        e.visit_end_date,
        e.visit_type_code,
        e.provider_id,
        e.care_site_id
      FROM encounters e
      INNER JOIN patients p ON e.patient_id = p.patient_id
      WHERE e.status = 'completed'
        AND p.active = 1
    
    target_table: visit_occurrence
    
    transformations:
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        validation:
          required: true
          data_type: integer
      
      - source_column: patient_id
        target_column: person_id
        type: direct
        validation:
          required: true
          data_type: integer
      
      - source_column: visit_start_date
        target_column: visit_start_datetime
        type: date_transform
        input_format: "%Y-%m-%d %H:%M:%S"
        output_format: "%Y-%m-%d %H:%M:%S"
        validation:
          required: true
      
      - source_column: visit_end_date
        target_column: visit_end_datetime
        type: date_transform
        input_format: "%Y-%m-%d %H:%M:%S"
        output_format: "%Y-%m-%d %H:%M:%S"
        validation:
          required: false
      
      - source_column: visit_type_code
        target_column: visit_concept_id
        type: vocabulary_mapping
        vocabulary: Visit
        mapping:
          "IP": 9201      # Inpatient Visit
          "OP": 9202      # Outpatient Visit
          "ER": 9203      # Emergency Room Visit
        default_value: 0
        validation:
          required: true
      
      # Static values for required fields
      - target_column: visit_type_concept_id
        type: static
        value: 44818517  # Visit derived from encounter on day
        validation:
          required: true

    validation_rules:
      - cross_field_rule: "visit_date_sequence"
        expression: "visit_end_datetime IS NULL OR visit_end_datetime >= visit_start_datetime"
        error_message: "Visit end date must be after or equal to start date"

  condition_occurrence:
    source_query: |
      SELECT 
        d.diagnosis_id,
        d.patient_id,
        d.encounter_id,
        d.diagnosis_code,
        d.diagnosis_system,
        d.diagnosis_date,
        d.diagnosis_type
      FROM diagnoses d
      INNER JOIN patients p ON d.patient_id = p.patient_id
      WHERE p.active = 1
    
    target_table: condition_occurrence
    
    transformations:
      - source_column: diagnosis_id
        target_column: condition_occurrence_id
        type: direct
        validation:
          required: true
          data_type: integer
      
      - source_column: patient_id
        target_column: person_id
        type: direct
        validation:
          required: true
          data_type: integer
      
      - source_column: encounter_id
        target_column: visit_occurrence_id
        type: direct
        validation:
          data_type: integer
      
      - source_column: diagnosis_date
        target_column: condition_start_datetime
        type: date_transform
        input_format: "%Y-%m-%d"
        output_format: "%Y-%m-%d 00:00:00"
        validation:
          required: true
      
      - source_column: diagnosis_code
        target_column: condition_concept_id
        type: conditional
        conditions:
          - when: "diagnosis_system == 'ICD10'"
            then:
              type: vocabulary_mapping
              vocabulary: ICD10CM
              target_vocabulary: SNOMED
              default_value: 0
          - when: "diagnosis_system == 'ICD9'"
            then:
              type: vocabulary_mapping
              vocabulary: ICD9CM
              target_vocabulary: SNOMED
              default_value: 0
          - default:
              type: static
              value: 0
        validation:
          required: true
      
      # Static values
      - target_column: condition_type_concept_id
        type: static
        value: 32020  # EHR encounter diagnosis
        validation:
          required: true

    validation_rules:
      - field: person_id
        type: foreign_key
        reference_table: person
        reference_column: person_id
        error_message: "Person ID must exist in person table"
      
      - field: visit_occurrence_id
        type: foreign_key
        reference_table: visit_occurrence
        reference_column: visit_occurrence_id
        required: false
        error_message: "Visit occurrence ID must exist in visit_occurrence table"
